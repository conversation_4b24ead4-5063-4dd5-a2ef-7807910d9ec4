import os

import pytz
from dotenv import load_dotenv

from config.models import *
from config.strategy import *

# [读取.env文件，转化为环境变量]
load_dotenv()

# [BASE]
TZ = pytz.timezone(os.getenv("TZ", "Asia/Shanghai"))
LOGGING_LEVEL = os.getenv("LOGGING_LEVEL", "INFO")
DEBUG = LOGGING_LEVEL == "DEBUG"
LOG_DIR = "../logs"

# [API_SERVER]
API_WORKER_COUNT = int(os.getenv("API_WORKER_COUNT", 4))
SYNC_THREAD_COUNT = int(os.getenv("SYNC_THREAD_COUNT", 600))

# [FILE_UPLOAD]
UPLOAD_CHUNK_SIZE = int(os.getenv("UPLOAD_CHUNK_SIZE", 10 * 1024 * 1024))  # 10MB
MAX_CONCURRENT_UPLOADS = int(os.getenv("MAX_CONCURRENT_UPLOADS", 5))

# [MYSQL]
MYSQL_HOST = os.getenv("MYSQL_HOST")
MYSQL_PORT = int(os.getenv("MYSQL_PORT", 3306))
MYSQL_USER = os.getenv("MYSQL_USER")
MYSQL_PASSWORD = os.getenv("MYSQL_PASSWORD")
MYSQL_DB = os.getenv("MYSQL_DB")
MYSQL_CHARSET = os.getenv("MYSQL_CHARSET")

# [REDIS]
REDIS_HOST = os.getenv("REDIS_HOST")
REDIS_PORT = int(os.getenv("REDIS_PORT", 6379))
REDIS_PASSWORD = os.getenv("REDIS_PASSWORD")
REDIS_CACHE_DB = int(os.getenv("REDIS_CACHE_DB", 0))
TASKIQ_BROKER_DB = int(os.getenv("TASKIQ_BROKER_DB", 1))

# [ES]
ES_URL = os.getenv("ES_URL")
ES_CONFLICT_RETRY = int(os.getenv("ES_CONFLICT_RETRY", 3))

# [MINIO]
MINIO_URL = os.getenv("MINIO_URL")
MINIO_ROOT_USER = os.getenv("MINIO_ROOT_USER")
MINIO_ROOT_PASSWORD = os.getenv("MINIO_ROOT_PASSWORD")

# [PK/SK]
SM2_PUBLIC_KEY = os.getenv("SM2_PUBLIC_KEY", "048ed5a0005b6f971dbb42ab88431303633fe962d551dcff977de3e404c9372bf3b0db8bdc408d182c17fc42c4a1a35b9a713eff0bb262dab033a50811d5dbd248")
SM2_PRIVATE_KEY = os.getenv("SM2_PRIVATE_KEY", "3445e2ccc242db2897ba502b7ddd51600acd7d674892d20fe896b97285253d1e")
SM4_KEY = os.getenv("SM4_KEY", "0D3CEF0DC381AE53A102156BDDC7D623")

# [JWT]
OAUTH_SECRET_KEY = os.getenv("SECRET_KEY", "59621bd1683ad541f0a6a4671db4431076fdb5998173fbc801967700d70b69ed")
OAUTH_ALGORITHM = os.getenv("ALGORITHM", "HS256")
ACCESS_TOKEN_EXPIRE_SECONDS = int(os.getenv("ACCESS_TOKEN_EXPIRE_SECONDS", 86400 * 7))

# [LLM]
LLM_API_URL = os.getenv("LLM_API_URL", "https://api.bchampion.cn/v1")
LLM_API_KEY = os.getenv("LLM_API_KEY", "sk-WhdXqG5JPEyN5zxeGvMCCboSrszkIxR0eKfLsd5uloNuurUI")

# [SEARCH]
TAVILY_API_URL = os.getenv("TAVILY_API_URL", "https://api.tavily.com/search")
TAVILY_API_KEY = os.getenv("TAVILY_API_KEY", None)

# [PARSER]
PARSER_FILE_SIZE = int(os.getenv("PARSER_FILE_SIZE", 30))  # MB
DOCLING_PARSER_URL = os.getenv("DOCLING_PARSER_URL")
MINERU_PARSER_URL = os.getenv("MINERU_PARSER_URL")
LIBREOFFICE_TIMEOUT = int(os.getenv("LIBREOFFICE_TIMEOUT", 600))

# [TAVILY]
TVLY_APIKEY = os.getenv("TVLY_APIKEY")

# [ZHIPU]
ZHIPU_SEARCH_URL = os.getenv("ZHIPU_SEARCH_URL")
ZHIPU_APIKEY = os.getenv("ZHIPU_APIKEY")

# [ARK_LLM]
ARK_API_KEY = os.getenv("ARK_API_KEY")
ARK_ENDPOINT_ID = os.getenv("ARK_ENDPOINT_ID")
ARK_TIMEOUT = int(os.getenv("ARK_TIMEOUT", 60 * 60))