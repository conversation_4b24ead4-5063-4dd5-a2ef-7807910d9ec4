#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import urllib3
from minio import Minio

from config import MINIO_URL, MINIO_ROOT_USER, MINIO_ROOT_PASSWORD, UPLOAD_CHUNK_SIZE, MAX_CONCURRENT_UPLOADS

# 配置连接池以提高并发性能和稳定性
import socket

http_client = urllib3.PoolManager(
    timeout=urllib3.Timeout(
        connect=15,  # 连接超时15s，给网络更多时间
        read=600     # 读取超时10分钟，适应大文件传输
    ),
    maxsize=10,      # 减少连接池大小，避免过多连接
    block=True,      # Ture: 当连接池满时是否阻塞  False: 创建新连接
    retries=urllib3.Retry(
        total=3,     # 增加重试次数
        backoff_factor=1.0,  # 增加重试间隔
        status_forcelist=[500, 502, 503, 504],  # 添加429限流状态码
        allowed_methods=["PUT", "POST", "GET", "HEAD"],
        # 添加连接错误重试
        raise_on_status=False
    )
)

minio_client = Minio(
    endpoint=MINIO_URL,
    access_key=MINIO_ROOT_USER,
    secret_key=MINIO_ROOT_PASSWORD,
    secure=False,
    http_client=http_client
)

repository_bucket = "repository"

# 导出配置常量供其他模块使用
__all__ = ['minio_client', 'repository_bucket', 'UPLOAD_CHUNK_SIZE', 'MAX_CONCURRENT_UPLOADS']