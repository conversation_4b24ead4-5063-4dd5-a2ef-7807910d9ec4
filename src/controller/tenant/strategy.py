#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import json

from pydantic import BaseModel, Field
from sqlalchemy import select

from engine.rdb import g, fetch_one, session_maker_sync
from engine.cache import r_cache
from model.strategy import StrategyModel
from controller.system.model_info import (DEFAULT_QWEN_30B_MODEL_ID,
                                          DEFAULT_BGE_M3_MODEL_ID,
                                          DEFAULT_BGE_RERANKER_V2_M3_MODEL_ID,
                                          ModelInfo)
from controller.engine import EmbeddingEngine, RerankEngine, RerankerModel, EmbeddingModel
from controller.retriever.base.rewrite import RewriteEngine, LLMModel


class ChatRetrieveStrategy(BaseModel):
    rewrite_model_id: int = Field(title="改写模型", default=DEFAULT_QWEN_30B_MODEL_ID)
    max_doc_size: int = Field(title="最大召回文档数量", ge=1, default=10)
    bm25_weight: float = Field(title="BM25权重", ge=0, le=1, default=1)
    embedding_weight: float = Field(title="向量权重", ge=0, le=1, default=1)
    rerank_model_id: int = Field(title="Rerank模型", default=DEFAULT_BGE_RERANKER_V2_M3_MODEL_ID)
    rerank_threshold: float = Field(title="Rerank模型阈值", default=0.8)
    topn: int = Field(title="最终保留文档片段", ge=1, default=20)

    model_config = {
        "extra": "ignore"  # 忽略额外字段，兼容既往历史
    }

class ParserStrategy(BaseModel):
    embedding_model_id: int = Field(title="向量模型ID", default=DEFAULT_BGE_M3_MODEL_ID)
    force_ocr: bool = Field(title="是否强制OCR", default=True)
    do_table_structure: bool = Field(title="是否提取表结构", default=True)
    do_formula_enrichment: bool = Field(title="是否提取latex表达式", default=True)
    include_images: bool = Field(title="是否解析文档中图片", default=False)
    images_scale: float = Field(title="图片缩放比例", default=2.0)


class StrategyConfig(BaseModel):
    chat_retrieve: ChatRetrieveStrategy = Field(title="Chat检索策略", default=ChatRetrieveStrategy())
    parser: ParserStrategy = Field(title="Parser策略", default=ParserStrategy())


class StrategyController:
    strategy_cache_name = "strategy_{tenant_id}"

    @staticmethod
    async def create(strategy: StrategyConfig, tenant_id: int = None):
        tenant_id = tenant_id or g.tenant_id
        assert tenant_id is not None, "未找到tenant_id"

        strategy = StrategyModel(tenant_id=tenant_id, config=strategy.model_dump())
        g.session.add(strategy)
        await g.session.flush()

        return strategy.id

    async def get(self, tenant_id: int = None):
        tenant_id = tenant_id or g.tenant_id
        assert tenant_id is not None, "未找到tenant_id"

        strategy = await self.get_cache(tenant_id=tenant_id)
        if strategy is None:
            query = (
                select(StrategyModel.config)
                .where(
                    StrategyModel.tenant_id == tenant_id,
                    StrategyModel.is_delete == 0)
                .order_by(StrategyModel.id.desc()))
            strategy_data = await fetch_one(query)
            strategy = strategy_data["config"]
            await self.set_cache(tenant_id=tenant_id, strategy=strategy)
        return strategy

    async def get_strategy(self, tenant_id: int = None):
        return StrategyConfig(**await self.get(tenant_id=tenant_id))

    async def get_parser_strategy(self, tenant_id: int = None):
        strategy = await self.get_strategy(tenant_id=tenant_id)
        parser_strategy = strategy.parser.model_dump()
        embedding_model_id = parser_strategy.pop("embedding_model_id")
        if embedding_model_id is not None:
            model_info = await ModelInfo.get_one(model_id=strategy.parser.embedding_model_id)
            embedding_engine = EmbeddingEngine(model=EmbeddingModel(model_info["model_name"]))
            embedding_batch_size = model_info["batch_size"]
        else:
            embedding_engine = None
            embedding_batch_size = 0

        parser_strategy["embedding_engine"] = embedding_engine
        parser_strategy["embedding_batch_size"] = embedding_batch_size

        return parser_strategy


    async def get_chat_retrieval_strategy(self, tenant_id: int = None):
        strategy = await self.get_strategy(tenant_id=tenant_id)
        chat_retrieval_strategy = strategy.chat_retrieve.model_dump()

        rewrite_model_id = chat_retrieval_strategy.pop("rewrite_model_id")
        rewrite_model = await ModelInfo.get_one(model_id=rewrite_model_id)
        chat_retrieval_strategy["rewrite_engine"] = RewriteEngine(LLMModel(rewrite_model["model_name"]))

        rerank_model_id = chat_retrieval_strategy.pop("rerank_model_id")
        rerank_model = await ModelInfo.get_one(model_id=rerank_model_id)
        chat_retrieval_strategy["rerank_engine"] = RerankEngine(RerankerModel(rerank_model["model_name"]))

        return chat_retrieval_strategy

    async def set_cache(self, tenant_id: int, strategy: dict | StrategyConfig):
        if isinstance(strategy, StrategyConfig):
            strategy = strategy.model_dump()
        name = self.strategy_cache_name.format(tenant_id=tenant_id)
        return await r_cache.set(name=name, value=json.dumps(strategy, ensure_ascii=False))

    async def get_cache(self, tenant_id: int):
        name = self.strategy_cache_name.format(tenant_id=tenant_id)
        res = await r_cache.get(name)
        if res is not None:
            return json.loads(res)
        return None

    @staticmethod
    def init_sync():
        with session_maker_sync() as session:
            session.add(StrategyModel(id=1, tenant_id=1, config=StrategyConfig().model_dump()))
            session.commit()


Strategy = StrategyController()
