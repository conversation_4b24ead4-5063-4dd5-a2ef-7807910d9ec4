#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import datetime
import hashlib
from pathlib import Path
from io import BytesIO
from enum import StrEnum
from typing import Optional, Annotated, List, Tuple

import asyncio
from sqlalchemy import select, update, func, or_, and_
from pydantic import BaseModel, Field, model_validator

from config.const import RepositoryDir
from engine.taskiq_task import broker
from engine.es import es
from engine.file_system import minio_client, repository_bucket, UPLOAD_CHUNK_SIZE, MAX_CONCURRENT_UPLOADS
from engine.rdb import g, query_order, paginator, fetch_one, fetch_all
from common.logger import logger
from common.time import now_datetime_str
from exception import NotFoundError, AlreadyExistsError
from model.repository import RepositoryModel, RepositoryType
from model.doc import DocModel, ALL_REPO_INDEX, DocStatus, get_index
from controller.operator.chunking.base import RetrieveChunkModel


class DoclingSupportFormat(StrEnum):
    """from docling.document_converter import InputFormat - 支持所有docling原生格式"""
    # 文档格式
    pdf = ".pdf"
    docx = ".docx"
    xlsx = ".xlsx"
    pptx = ".pptx"
    html = ".html"
    htm = ".htm"
    md = ".md"
    txt = ".txt"

    # 图片格式 (所有图像格式都映射到 InputFormat.IMAGE)
    jpg = ".jpg"
    jpeg = ".jpeg"
    png = ".png"
    bmp = ".bmp"
    tiff = ".tiff"
    tif = ".tif"

    # 数据格式
    csv = ".csv"

    # 其他文档格式
    asciidoc = ".adoc"
    asc = ".asc"


class LibreOfficeSupportFormat(StrEnum):
    """LibreOffice 支持转换为 PDF 的文档格式"""

    # Microsoft Office 格式
    doc = ".doc"
    docx = ".docx"
    xls = ".xls"
    xlsx = ".xlsx"
    ppt = ".ppt"
    pptx = ".pptx"

    # LibreOffice/OpenOffice 格式
    odt = ".odt"
    ods = ".ods"
    odp = ".odp"

    # 其他文档格式
    rtf = ".rtf"
    txt = ".txt"
    html = ".html"
    xml = ".xml"
    epub = ".epub"
    fb2 = ".fb2"
    md = ".md"
    csv = ".csv"

    # 图像格式
    jpg = ".jpg"
    jpeg = ".jpeg"
    png = ".png"
    gif = ".gif"
    bmp = ".bmp"
    tiff = ".tiff"
    svg = ".svg"

    # 页面描述格式
    ps = ".ps"
    eps = ".eps"


class LegacyOfficeFormat(StrEnum):
    # word类
    doc = ".doc"

    # ppt类
    ppt = ".ppt"

    # excel类
    xls = ".xls"

class SpecialSupportFormat(StrEnum):
    # 其他文档
    txt = ".txt"


class SupportFormat(StrEnum):
    """合并了所有支持的文件后缀的枚举类"""
    # DoclingSupportSuffix成员 - 文档格式
    pdf = DoclingSupportFormat.pdf
    docx = DoclingSupportFormat.docx
    xlsx = DoclingSupportFormat.xlsx
    pptx = DoclingSupportFormat.pptx
    html = DoclingSupportFormat.html
    htm = DoclingSupportFormat.htm
    md = DoclingSupportFormat.md
    txt = DoclingSupportFormat.txt
    csv = DoclingSupportFormat.csv
    asciidoc = DoclingSupportFormat.asciidoc
    asc = DoclingSupportFormat.asc

    # DoclingSupportSuffix成员 - 图像格式
    jpg = DoclingSupportFormat.jpg
    jpeg = DoclingSupportFormat.jpeg
    png = DoclingSupportFormat.png
    bmp = DoclingSupportFormat.bmp
    tiff = DoclingSupportFormat.tiff
    tif = DoclingSupportFormat.tif

    # LibreofficePreConvertSupportSuffix成员 (需要预转换)
    doc = LegacyOfficeFormat.doc
    ppt = LegacyOfficeFormat.ppt
    xls = LegacyOfficeFormat.xls


class ReferenceType(StrEnum):
    """文档溯源类型"""
    pdf = "pdf"
    html = "html"


class TextDoc(BaseModel):
    doc_id: Annotated[Optional[int], Field(title="RDB文档ID")] = None  # 后续必须补充
    data_time: Annotated[str, Field(title="数据时间")]
    repo_id: Annotated[int, Field(title="知识库ID")]
    tenant_id: Annotated[Optional[int], Field(title="租户ID")] = None
    doc_type: Annotated[str, Field(title="文档类型")] = "text"
    title: Annotated[str, Field(title="文档标题")]
    content: Annotated[str, Field(title="文档正文")]
    html_content: Annotated[str, Field(title="文档正文(HTML格式)")] = None
    source: Annotated[Optional[str], Field(title="文档来源")] = None
    url: Annotated[Optional[str], Field(title="文档链接")] = None
    author: Annotated[Optional[str], Field(title="文档作者")] = None
    tags: Annotated[Optional[List[str]], Field(title="原文标签")] = []
    reference_type: Annotated[ReferenceType, Field(title="溯源类型")] = ReferenceType.html

    filename: Annotated[Optional[str], Field(title="文件名称")] = None
    md5: Annotated[Optional[str], Field(title="文本唯一编码")] = None

    @model_validator(mode="after")
    def check_total(self):
        self.title = self.title.strip()
        self.content = self.content.strip()

        if not self.md5:
            self.md5 = hashlib.sha256(self.content.encode("utf-8")).hexdigest()

        if not self.filename:
            self.filename = self.title

        return self


class DocController:
    @staticmethod
    def get_query(repo_id: int = None, doc_id: int = None, doc_ids: List[int] = None, user_id: int = None,
                  md5: str = None, match: str = None, start: str = None, end: str = None, is_delete: bool = False,
                  order_by: str = None):
        where = []
        if g.tenant_id:
            where.append(RepositoryModel.tenant_id == g.tenant_id)
        if repo_id is not None:
            where.append(DocModel.repo_id == repo_id)
        if doc_id is not None:
            where.append(DocModel.id == doc_id)
        if doc_ids is not None:
            where.append(DocModel.id.in_(doc_ids))
        if user_id is not None:
            where.append(
                or_(
                    RepositoryModel.type_ == RepositoryType.public,
                    and_(RepositoryModel.type_ == RepositoryType.private,
                         RepositoryModel.create_user_id == user_id)
                )
            )
        if md5:  # md5必须有值才可比较
            where.append(DocModel.md5 == md5)
        if match:
            where.append(DocModel.name.ilike(f"%{match}%"))
        if start:
            where.append(DocModel.create_time >= start)
        if end:
            where.append(DocModel.create_time < end)
        if is_delete is not None:
            where.append(RepositoryModel.is_delete == int(is_delete))
            where.append(DocModel.is_delete == int(is_delete))

        query = (
            select(
                DocModel.id.label("doc_id"),
                DocModel.repo_id,
                DocModel.name,
                DocModel.path,
                DocModel.tags,
                DocModel.size,
                DocModel.status,
                DocModel.data_time,
                DocModel.sentiment_score,
                DocModel.keywords,
                DocModel.reference_type,
                DocModel.create_user_id,
                DocModel.create_time,
                DocModel.update_time,
                RepositoryModel.tenant_id,
                RepositoryModel.name.label("repo_name"),
                RepositoryModel.type_.label("repo_type"))
            .join(RepositoryModel, DocModel.repo_id == RepositoryModel.id)
            .where(*where)
        )
        query = query_order(query=query, order_by=order_by, table=DocModel)

        return query

    async def get_list(self, repo_id: int = None, user_id: int = None, match: str = None, page: int = None,
                       per_page: int = None, order_by: str = None):
        query = self.get_query(repo_id=repo_id, user_id=user_id, match=match, order_by=order_by)
        pager, docs = await paginator(query, page=page, per_page=per_page)

        return pager, docs

    async def get_one(self, doc_id: int = None, user_id: int = None, md5: str = None, repo_id: int = None):
        query = self.get_query(doc_id=doc_id, user_id=user_id, repo_id=repo_id, md5=md5)
        return await fetch_one(query)

    async def get_id_name_mapping(self, doc_ids: List[int], is_delete: bool = False):
        query = self.get_query(doc_ids=doc_ids, is_delete=is_delete)
        docs = await fetch_all(query)
        return {doc["doc_id"]: doc["name"] for doc in docs}

    async def get_count(self, repo_id: int = None, doc_id: int = None, doc_ids: List[int] = None, md5: str = None,
                        match: str = None, start: str = None, end: str = None, is_delete: bool = False):
        query = self.get_query(
            repo_id=repo_id, doc_id=doc_id, doc_ids=doc_ids, md5=md5, match=match, start=start, end=end,
            is_delete=is_delete)
        return await fetch_one(select(func.count(query.subquery().c.doc_id).label("total")))

    @staticmethod
    def get_es_query(repo_id: int = None, repo_ids: List[int] = None, doc_ids: List[int] = None,
                     match: str | List[str] = None, start_time: str | datetime.datetime = None,
                     end_time: str | datetime.datetime = None):
        filters = []
        if repo_id is not None:
            filters.append({"term": {"repo_id": repo_id}})
        if repo_ids is not None:
            filters.append({"terms": {"repo_id": repo_ids}})
        if g.tenant_id is not None:
            filters.append({"term": {"tenant_id": g.tenant_id}})
        if doc_ids is not None:
            filters.append({"terms": {"_id": doc_ids}})
        if (start_time is not None) or (end_time is not None):
            range_time = {}
            if start_time:
                range_time["gte"] = start_time if isinstance(start_time, str) else start_time.strftime("%Y-%m-%d %H:%M:%S")
            if end_time:
                range_time["lt"] = end_time if isinstance(end_time, str) else end_time.strftime("%Y-%m-%d %H:%M:%S")
            filters.append({"range": {"data_time": range_time}})
        if match:
            if isinstance(match, str):
                match = [match]
            match_should = []
            for kw in match:
                match_should.append({
                    "multi_match": {
                        "query": kw,
                        "fields": ["filename", "author", "plain_text", "tags", "source"],
                        "type": "phrase"
                    }
                })
            filters.append({"bool": {"should": match_should}})

        query = {
            "bool": {
                "filter": filters,
            }
        }
        # print(json.dumps(query, indent=4, ensure_ascii=False))
        return query

    async def get_es_all(self, repo_ids: List[int] = None, doc_ids: List[int] = None, match: str | List[str] = None,
                         start_time: str | datetime.datetime = None, end_time: str | datetime.datetime = None,
                         size: int = 10000, order_by: str = None, includes: List[str] = None):
        index = get_index(repo_ids=repo_ids)
        query = self.get_es_query(repo_ids=repo_ids, doc_ids=doc_ids, start_time=start_time,
            end_time=end_time, match=match)

        if order_by:
            if isinstance(order_by, str):
                order_by = [{ob.split(":")[0]: ob.split(":")[1]} for ob in order_by.split(",")]
        else:
            order_by = [{"data_time": "desc"}, {"create_time": "asc"}]

        docs = []
        if size <= 10000:
            search_result = await es.search(
                index=index,
                query=query,
                size=size,
                _source_includes=includes,
                sort=order_by,
                track_total_hits=False)

            if len(search_result["hits"]["hits"]) > 0:
                docs = [doc["_source"] for doc in search_result["hits"]["hits"]]
        else:
            pit = await es.open_point_in_time(index=index, keep_alive="1m")
            pit_id = pit["id"]

            search_after = None

            while True:
                search_result = await es.search(
                    query=query,
                    size=10000,
                    _source_includes=includes,
                    sort=order_by,
                    pit={"id": pit_id, "keep_alive": "1m"},
                    search_after=search_after,
                    track_total_hits=False)

                if len(search_result["hits"]["hits"]) > 0:
                    search_after = search_result["hits"]["hits"][-1]["sort"]
                    pit_id = search_result["pit_id"]
                    source_data = [doc["_source"] for doc in search_result["hits"]["hits"]]
                    docs.extend(source_data)
                    if len(docs) >= size:
                        docs = docs[:size]
                        break
                    continue

                await es.close_point_in_time(body={"id": pit_id})
                break

        return docs

    async def get_full_doc_retrieve(self, doc_ids: List[int]) -> List[RetrieveChunkModel]:
        es_docs = await self.get_es_all(
            doc_ids=doc_ids,
            size=len(doc_ids),
            includes=["html", "plain_text", "doc_id", "filename", "data_time", "tokens_counts", "url", "icon"])

        for doc in es_docs:
            doc["html_content"] = doc.pop("html")
            doc["plain_content"] = doc.pop("plain_text")
            doc.setdefault("token_counts", len(doc["html_content"]))  # 防止因为历史原因没有默认token
        return [RetrieveChunkModel(cid=f"{doc['doc_id']}_0", xpath=[], web_search=False, **doc) for doc in es_docs]

    async def get_es_list(self, repo_id: int = None, match: str = None, page: int = None, per_page: int = None):
        query = self.get_es_query(repo_id=repo_id, match=match)
        per_page = per_page or 20
        res = await es.search(
            index=ALL_REPO_INDEX,
            query=query,
            sort=[{"data_time": "desc"}, {"create_time": "asc"}],
            from_=(page - 1) * per_page if page else None,
            size=per_page or 20 if page else None,
            source_includes=["title", "data_time", "tags", "extract_result.sentiment_score", "extract_result.keywords"])

        hits = res["hits"]["hits"]
        docs = []
        for hit in hits:
            doc = {
                "doc_id": hit["_id"],
                **hit["_source"]
            }
            # todo:临时方法
            doc.setdefault("tags", [])
            doc.setdefault("extract_result", {})
            doc["extract_result"].setdefault("sentiment_score", None)
            doc["extract_result"].setdefault("keywords", None)
            docs.append(doc)

        return docs

    @staticmethod
    async def get_es_one(repo_id: int, doc_id: int, includes: List[str] = None, excludes: List[str] = None):
        index = get_index(repo_ids=[repo_id])

        doc = await es.get(
            index=index,
            id=doc_id,
            source_includes=includes,
            source_excludes=excludes)
        if not doc:
            raise NotFoundError(message="未找到指定ES文档")

        _source = doc["_source"]
        return _source

    @staticmethod
    async def get_es_chunks(citation_ids: List[str]):
        res = await es.search(
            index=ALL_REPO_INDEX,
            query={
                "nested": {
                    "path": "chunks",
                    "query": {
                        "terms": {"chunks.cid": citation_ids}
                    },
                    "inner_hits": {
                        "_source": ["chunks.cid", "chunks.html_content", "chunks.xpath"]
                    }
                }
            },
            source_includes=["doc_id", "repo_id"],
            size=10000
            )

        chunks = [{**hit["_source"], **chunk["_source"]} for hit in res["hits"]["hits"] for chunk in hit["inner_hits"]["chunks"]["hits"]["hits"]]

        return chunks

    @staticmethod
    async def get_object(filepath: Path):
        """
        获取S3对象内容

        Args:
            filepath: S3文件路径

        Returns:
            str: 文件内容
        """
        def _get_object():
            return minio_client.get_object(
                bucket_name=repository_bucket,
                object_name=filepath.as_posix()
            )

        with await asyncio.to_thread(_get_object) as response:
            return response.read().decode("utf-8")

    async def create(self, repo_id: int, name: str, size: int, md5: str, status: DocStatus, tags: List[str] = None,
                     info: dict = None, create_time: str = None, update_time: str = None, data_time: str = None,
                     keywords: List[str] = None, sentiment_score: float = None,
                     reference_type: ReferenceType = ReferenceType.html, repetition_strategy: str = "raise"):
        if doc := await self.get_one(repo_id=repo_id, md5=md5):
            if repetition_strategy == "return":
                logger.warning(f"Doc[{repo_id}]: 文档已存在,根据重复策略返回存在id: [{doc['doc_id']}]")
                return doc["doc_id"]
            else:
                raise AlreadyExistsError(message=f"文档已存在: {doc['name']}")

        tags = tags or []
        info = info or {}
        create_time = create_time or now_datetime_str()
        update_time = update_time or now_datetime_str()
        data_time = data_time or create_time

        doc = DocModel(
            repo_id=repo_id, name=name, path="", size=size, md5=md5, status=status, tags=tags, info=info,
            create_time=create_time, data_time=data_time, update_time=update_time, keywords=keywords,
            sentiment_score=sentiment_score, reference_type=reference_type)
        g.session.add(doc)
        await g.session.flush()

        return doc.id

    @staticmethod
    async def update(doc_id: int, md5: str = None, status: DocStatus = None, path: Path = None, tags: List[str] = None,
                     data_time: str = None, sentiment_score: float = None, keywords: List[str] = None,
                     reference_type: str = None):
        update_info = {}
        if md5 is not None:
            update_info[DocModel.md5] = md5
        if status is not None:
            update_info[DocModel.status] = status
        if path is not None:
            update_info[DocModel.path] = path.as_posix()
        if tags is not None:
            update_info[DocModel.tags] = tags
        if data_time is not None:
            update_info[DocModel.data_time] = data_time
        if sentiment_score is not None:
            sentiment_score = round(sentiment_score, 2)
            update_info[DocModel.sentiment_score] = sentiment_score
        if keywords is not None:
            update_info[DocModel.keywords] = keywords
        if reference_type is not None:
            update_info[DocModel.reference_type] = reference_type

        query = (update(DocModel)
                 .where(DocModel.id == doc_id)
                 .values(update_info))
        await g.session.execute(query)

    @staticmethod
    async def delete(repo_id: int, doc_id: str):
        query = (update(DocModel)
                 .where(DocModel.id == doc_id)
                 .values({DocModel.is_delete: 1}))
        await g.session.execute(query)

        index = get_index(repo_ids=[repo_id])
        await es.delete(index=index, id=doc_id)

    @staticmethod
    async def stats_repo_doc_counts():
        query = (
            select(
                DocModel.repo_id,
                func.count(DocModel.id).label("doc_count"))
            .where(DocModel.is_delete == 0)
            .group_by(DocModel.repo_id)
        )
        return await fetch_all(query)

    @staticmethod
    def get_path(repo_id: int, doc_id: int, filename: str):
        return RepositoryDir / str(repo_id) / str(doc_id) / filename

    @staticmethod
    async def upload_s3(path: Path, content: bytes):
        """
        上传文件到S3存储

        Args:
            path: S3存储路径
            content: 文件内容字节
        """
        def _upload():
            # 根据文件大小选择上传策略
            content_length = len(content)

            if content_length > UPLOAD_CHUNK_SIZE:
                # 大文件使用分块上传
                logger.info(f"大文件分块上传 ({content_length / 1024 / 1024:.2f}MB): {path.as_posix()}")
                minio_client.put_object(
                    bucket_name=repository_bucket,
                    object_name=path.as_posix(),
                    data=BytesIO(content),
                    length=content_length,
                    part_size=UPLOAD_CHUNK_SIZE
                )
            else:
                # 小文件直接上传
                logger.info(f"小文件上传 ({content_length / 1024 / 1024:.2f}MB): {path.as_posix()}")
                minio_client.put_object(
                    bucket_name=repository_bucket,
                    object_name=path.as_posix(),
                    data=BytesIO(content),
                    length=content_length
                )

        await asyncio.to_thread(_upload)

    async def upload_s3_batch(self, uploads: List[Tuple[int, Path, bytes]], max_concurrent: int = None):
        """
        批量上传文件，控制并发数量

        Args:
            uploads: 上传任务列表，每个元素为 (int, path, content) 元组
                int: 任务id
                path: S3存储路径
                content: 文件内容字节
            max_concurrent: 最大并发数，默认使用配置值

        Returns:
            List[bool]: 每个文件的上传结果，True表示成功，False表示失败
        """
        if max_concurrent is None:
            max_concurrent = MAX_CONCURRENT_UPLOADS

        semaphore = asyncio.Semaphore(max_concurrent)

        async def _upload_single(path: Path, content: bytes) -> bool:
            async with semaphore:
                try:
                    await self.upload_s3(path, content)
                    return True
                except Exception as e:
                    logger.error(f"批量上传失败: {path.as_posix()}, 错误: {str(e)}")
                    raise False

        tasks = [_upload_single(path, content) for _id, path, content in uploads]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        logger.info(f"批量上传完成: {sum(results)} / {len(uploads)} 成功")

        return [(t[0], results[i]) for i, t in enumerate(uploads)]

    async def send_doc_parsing_task(self, doc_id: int):
        await self.doc_parsing.kiq(doc_id=doc_id)
        logger.info(f"Doc[{doc_id}]: 文档解析任务已发送")

    # tasks.doc_parsing的占位方法
    @staticmethod
    @broker.task(task_name="doc_parsing")
    async def doc_parsing(doc_id: int): ...

    # tasks.doc_extract的占位方法
    @staticmethod
    @broker.task(task_name="doc_extract")
    async def doc_extract(doc_id: int): ...


Doc = DocController()
