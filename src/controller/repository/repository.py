#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import time
from sqlalchemy import select, update, or_, and_, func, case, Subquery

from engine.rdb import session_maker_sync, paginator, query_order, fetch_all, fetch_one, g
from engine.cache import r_cache
from engine.es import es, es_sync
from exception import ApiError
from model.repository import RepositoryModel, RepositoryType
from model.doc import DocModel
from model.doc import DOC_MAPPING, get_index
from common.logger import logger


DefaultRepoDataRange = [
    {"code": "300750", "code_name": "宁德时代", "market": "sh", "repo_id": -21},
    {"code": "601012", "code_name": "隆基绿能", "market": "sh", "repo_id": -22},
]


class RepositoryController:
    recent_zset_name = "repo:recent_{user_id}"

    @staticmethod
    def get_query(repo_id: int = None, repo_ids: list[int] = None, type_: RepositoryType = None,
                  match: str = None, user_id: int = None, is_delete: bool = False, order_by: str = None):
        where = []
        if g.tenant_id is not None:
            where.append(RepositoryModel.tenant_id == g.tenant_id)
        if repo_id is not None:
            where.append(RepositoryModel.id == repo_id)
        if repo_ids is not None:
            where.append(RepositoryModel.id.in_(repo_ids))
        if match:
            where.append(RepositoryModel.name.ilike(f"%{match}%"))
        if user_id:
            where.append(
                or_(
                    RepositoryModel.type_ == RepositoryType.public,
                    and_(RepositoryModel.type_ == RepositoryType.private,
                         RepositoryModel.create_user_id == user_id)
                )
            )
        if type_:
            where.append(RepositoryModel.type_ == type_)
        if is_delete is not None:
            where.append(RepositoryModel.is_delete == is_delete)

        doc_query = (
            select(
                DocModel.repo_id,
                func.count(DocModel.id).label("doc_count"),
                func.max(DocModel.update_time).label("doc_max_update_time")
            )
            .where(DocModel.is_delete == 0)
            .group_by(DocModel.repo_id)
        ).subquery()

        query = (
            select(
                RepositoryModel.id.label("repo_id"),
                RepositoryModel.name,
                RepositoryModel.type_,
                RepositoryModel.extract_model_id,
                RepositoryModel.create_time,
                RepositoryModel.create_user_id,
                # 当文档最大更新时间存在且大于仓库更新时间，使用文档更新时间,否则使用知识库时间
                case(
                    (
                        (doc_query.c.doc_max_update_time > RepositoryModel.update_time, doc_query.c.doc_max_update_time)
                    ), else_=RepositoryModel.update_time).label("update_time"),
                func.coalesce(doc_query.c.doc_count, 0).label("doc_count"))
            .join(doc_query, RepositoryModel.id == doc_query.c.repo_id, isouter=True)
            .where(*where))

        query = query_order(query=query, order_by=order_by, table=RepositoryModel)

        return query

    async def get_one(self, repo_id: int, user_id: int = None):
        query = self.get_query(repo_id=repo_id, user_id=user_id)
        repo = await fetch_one(query=query)

        if repo:
            repo["edit"] = self.check_user_editable(repo=repo, user_id=user_id)
        return repo

    async def get_list(self, repo_id: int = None, repo_ids: list[int] = None, type_: RepositoryType = None,
                       match: str = None, user_id: int = None, page: int = None, per_page: int = None,
                       order_by: str = None):
        query = self.get_query(
            repo_id=repo_id, repo_ids=repo_ids, type_=type_, match=match, user_id=user_id, order_by=order_by)
        pager, repos = await paginator(query=query, page=page, per_page=per_page)

        for repo in repos:
            repo["edit"] = self.check_user_editable(repo=repo, user_id=user_id)

        return pager, repos

    async def get_all(self, repo_id: int = None, repo_ids: list[int] = None, type_: str = None, match: str = None,
                      user_id: int = None, order_by: str = None):
        query = self.get_query(
            repo_id=repo_id, repo_ids=repo_ids, type_=type_, match=match, user_id=user_id, order_by=order_by)
        repos = await fetch_all(query=query)

        for repo in repos:
            repo["edit"] = self.check_user_editable(repo=repo, user_id=user_id)

        return repos

    async def get_id_name_mapping(self, repo_ids: list[int], is_delete: bool = False):
        query = self.get_query(repo_ids=repo_ids, is_delete=is_delete)
        repos = await fetch_all(query)
        return {repo["repo_id"]: repo["name"] for repo in repos}

    async def create(self, name: str, type_: RepositoryType, extract_model_id: int, tenant_id: int = None):
        tenant_id = tenant_id or g.tenant_id

        repo = RepositoryModel(tenant_id=tenant_id, name=name, type_=type_, extract_model_id=extract_model_id)
        g.session.add(repo)
        await g.session.flush()

        repo_id = repo.id

        await self.create_es_index(repo_id=repo_id)

        return repo.id

    def init_sync(self):
        with session_maker_sync() as session:
            for stock in DefaultRepoDataRange:
                repo_id = stock["repo_id"]
                session.add(
                    RepositoryModel(
                        id=repo_id, tenant_id=1, name=stock["code_name"], type_=RepositoryType.public,
                        extract_model_id=1))
                self.init_es_index_sync(repo_id=repo_id)
                session.commit()

    @staticmethod
    async def update(repo_id: int, name: str = None, type_: RepositoryType = None, extract_model_id: int = None):
        update_info = {}
        if name is not None:
            update_info[RepositoryModel.name] = name
        if type_ is not None:
            update_info[RepositoryModel.type_] = type_
        if extract_model_id is not None:
            update_info[RepositoryModel.extract_model_id] = extract_model_id
        query = (update(RepositoryModel)
                 .where(RepositoryModel.id == repo_id)
                 .values(update_info))

        await g.session.execute(query)

    async def delete(self, repo_id: int):
        query = (update(RepositoryModel)
                 .where(RepositoryModel.id == repo_id)
                 .values({RepositoryModel.is_delete: 1}))
        await g.session.execute(query)

        await self.delete_docs(repo_id=repo_id)
        await self.delete_es_index(repo_id=repo_id)

    @staticmethod
    async def delete_docs(repo_id: int):
        query = (update(DocModel)
                 .where(DocModel.repo_id == repo_id)
                 .values({DocModel.is_delete: 1}))
        await g.session.execute(query)

    @staticmethod
    async def create_es_index(repo_id: int):
        index = get_index(repo_ids=[repo_id])

        if await es.indices.exists(index=index):
            raise ApiError(message=f"ES索引[{index}]已存在,请检查逻辑是否有误")
        try:
            res = await es.indices.create(index=index, body=DOC_MAPPING)
        except Exception as err:
            raise ApiError(f"ES索引创建失败: {str(err)}")
        else:
            if res["acknowledged"] is True:
                logger.info(f"ES索引 [{index}] 创建成功")
            else:
                raise ApiError(f"ES索引创建失败: {str(res)}")

    @staticmethod
    def init_es_index_sync(repo_id: int):
        index =get_index(repo_ids=[repo_id])

        if es_sync.indices.exists(index=index):
            return
        try:
            res = es_sync.indices.create(index=index, body=DOC_MAPPING)
        except Exception as err:
            raise ApiError(f"ES索引创建失败: {str(err)}")
        else:
            if res["acknowledged"] is True:
                logger.info(f"ES索引 [{index}] 创建成功")
            else:
                raise ApiError(f"ES索引创建失败: {str(res)}")

    @staticmethod
    async def delete_es_index(repo_id: int):
        index = get_index(repo_ids=[repo_id])

        try:
            res = await es.indices.delete(index=index)
        except Exception as err:
            raise ApiError(f"ES删除索引失败: {str(err)}")
        else:
            if res["acknowledged"] is True:
                logger.info(f"ES索引 [{index}] 删除成功")
            else:
                raise ApiError(f"ES索引删除失败: {str(res)}")

    async def set_recent(self, user_id: int, repo_id: int):
        await r_cache.zadd(name=self.recent_zset_name.format(user_id=user_id), mapping={repo_id: int(time.time())})

    async def get_recent(self, user_id: int, size: int = 4):
        res = await r_cache.zrevrange(name=self.recent_zset_name.format(user_id=user_id), start=0, end=size-1)
        if res is None:
            return []
        return [int(r) for r in res]

    @staticmethod
    def check_user_editable(repo: dict, user_id: int) -> bool:
        return user_id is None or user_id == repo["create_user_id"]

Repo = RepositoryController()
