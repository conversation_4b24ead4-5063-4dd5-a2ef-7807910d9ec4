from autogen_core.models import ModelFamily
from autogen_ext.models.openai import OpenAIChatCompletionClient

from config import LLM_API_URL, LLM_API_KEY, LLMModel


gpt_41 = OpenAIChatCompletionClient(
    base_url=LLM_API_URL,
    model=LLMModel.GPT_41,
    api_key=LLM_API_KEY,
    temperature=0,
)

o4_mini = OpenAIChatCompletionClient(
    base_url=LLM_API_URL,
    model=LLMModel.O4_MINI,
    api_key=LLM_API_KEY,
)

gemini_2_5_flash_lite = OpenAIChatCompletionClient(
    base_url=LLM_API_URL,
    model=LLMModel.GEMINI_2_5_FLASH_LITE,
    api_key=LLM_API_KEY,
    temperature=0,
    model_info={
        "vision": True,
        "function_calling": True,
        "json_output": True,
        "family": ModelFamily.UNKNOWN,
        "structured_output": True,
    },
)

gemini_2_5_flash = OpenAIChatCompletionClient(
    base_url=LLM_API_URL,
    model=LLMModel.GEMINI_2_5_FLASH,
    api_key=LLM_API_KEY,
    temperature=0,
    model_info={
        "vision": True,
        "function_calling": True,
        "json_output": True,
        "family": ModelFamily.UNKNOWN,
        "structured_output": True,
    },
)

gemini_2_5_pro = OpenAIChatCompletionClient(
    base_url=LLM_API_URL,
    model=LLMModel.GEMINI_2_5_PRO,
    api_key=LLM_API_KEY,
    temperature=0,
    model_info={
        "vision": True,
        "function_calling": True,
        "json_output": True,
        "family": ModelFamily.GEMINI_2_5_PRO,
        "structured_output": True,
    },
)

deepseek_v3_0324 = OpenAIChatCompletionClient(
    base_url=LLM_API_URL,
    model=LLMModel.DEEPSEEK_V3_0324,
    api_key=LLM_API_KEY,
    temperature=0,
    model_info={
        "vision": False,
        "function_calling": True,
        "json_output": True,
        "family": ModelFamily.UNKNOWN,
        "structured_output": False,
    },
)

deepseek_r1_0528 = OpenAIChatCompletionClient(
    base_url=LLM_API_URL,
    model=LLMModel.DEEPSEEK_R1_0528,
    api_key=LLM_API_KEY,
    model_info={
        "vision": False,
        "function_calling": True,
        "json_output": True,
        "family": ModelFamily.R1,
        "structured_output": False,
    },
)

deepseek_v3_1_0821 = OpenAIChatCompletionClient(
    base_url=LLM_API_URL,
    model=LLMModel.DEEPSEEK_V3_1_0821,
    api_key=LLM_API_KEY,
    temperature=0,
    model_info={
        "vision": False,
        "function_calling": True,
        "json_output": True,
        "family": ModelFamily.UNKNOWN,
        "structured_output": False,
    },
)

deepseek_v3_1_0821_thinking = OpenAIChatCompletionClient(
    base_url=LLM_API_URL,
    model=LLMModel.DEEPSEEK_V3_1_0821_THINKING,
    api_key=LLM_API_KEY,
    temperature=0.7,
    model_info={
        "vision": False,
        "function_calling": True,
        "json_output": True,
        "family": ModelFamily.UNKNOWN,
        "structured_output": False,
    },
)