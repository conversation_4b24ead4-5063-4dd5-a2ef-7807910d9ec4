#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from config import LLMModel, EmbeddingModel, RerankerModel
from engine.rdb import g, session_maker_sync, query_order, fetch_all, fetch_one
from model.model_info import ModelInfoModel, ModelType

from sqlalchemy import select, update


DEFAULT_QWEN_30B_MODEL_ID = 3
DEFAULT_BGE_M3_MODEL_ID = 6
DEFAULT_BGE_RERANKER_V2_M3_MODEL_ID = 7


class ModelInfoController:
    @staticmethod
    def get_query(model_id: int = None, model_ids: list[int] = None, model_name: str = None,
                  model_type: ModelType = None, chat: bool = None, thinking: bool = None, deep_research: bool = None,
                  extract: bool = None, abstract: bool = None, analyze: bool = None, order_by: str = None):
        where = [ModelInfoModel.is_delete == 0]
        if model_id is not None:
            where.append(ModelInfoModel.id == model_id)
        if model_ids is not None:
            where.append(ModelInfoModel.id.in_(model_ids))
        if model_name:
            where.append(ModelInfoModel.model_name == model_name)
        if model_type is not None:
            where.append(ModelInfoModel.model_type == model_type)
        if chat is not None:
            where.append(ModelInfoModel.chat == chat)
        if thinking is not None:
            where.append(ModelInfoModel.thinking == thinking)
        if deep_research is not None:
            where.append(ModelInfoModel.deep_research == deep_research)
        if extract is not None:
            where.append(ModelInfoModel.extract == extract)
        if abstract is not None:
            where.append(ModelInfoModel.abstract == abstract)
        if analyze is not None:
            where.append(ModelInfoModel.analyze == analyze)

        query = (
            select(
                ModelInfoModel.id.label("model_id"),
                ModelInfoModel.show_name,
                ModelInfoModel.model_name,
                ModelInfoModel.icon,
                ModelInfoModel.describe,
                ModelInfoModel.url,
                # ModelInfoModel.apikey,
                ModelInfoModel.model_type,
                ModelInfoModel.max_input_tokens,
                ModelInfoModel.batch_size,
                ModelInfoModel.function_calling,
                ModelInfoModel.json_output,
                ModelInfoModel.structured_output,
                ModelInfoModel.concurrency,
                ModelInfoModel.extra,
                ModelInfoModel.chat,
                ModelInfoModel.thinking,
                ModelInfoModel.deep_research,
                ModelInfoModel.extract,
                ModelInfoModel.abstract,
                ModelInfoModel.analyze)
            .where(*where))

        query = query_order(query=query, table=ModelInfoModel, order_by=order_by)
        return query

    async def get_one(self, model_id: int = None, model_name: str = None):
        assert model_id or model_name, "model_id or model_name must be provided"
        query = self.get_query(model_id=model_id, model_name=model_name)
        return await fetch_one(query=query)

    async def get_all(self, model_ids: list = None, model_type: ModelType = None, chat: bool = None,
                      thinking: bool = None, deep_research: bool = None, extract: bool = None, abstract: bool = None,
                      analyze: bool = None, order_by: str = None):
        query = self.get_query(
            model_ids=model_ids, model_type=model_type, chat=chat, thinking=thinking, deep_research=deep_research,
            extract=extract, abstract=abstract, analyze=analyze, order_by=order_by)
        return await fetch_all(query=query)

    async def get_id_mapping(self, model_ids: list = None, model_type: ModelType = None, chat: bool = None,
                      thinking: bool = None, deep_research: bool = None, extract: bool = None, abstract: bool = None,
                      analyze: bool = None, order_by: str = None):
        models = await self.get_all(model_ids=model_ids, model_type=model_type, chat=chat, thinking=thinking,
                               deep_research=deep_research, extract=extract, abstract=abstract, analyze=analyze,
                               order_by=order_by)
        return {model["model_id"]: {"model_id": model["model_id"], "show_name": model["show_name"]} for model in models}


    @staticmethod
    async def create(show_name: str, model_name: str, describe: str, url: str, apikey: str, model_type: str,
                     max_input_tokens: int, batch_size: int, function_calling: bool, json_output: bool,
                     structured_output: bool, concurrency: int, extra: dict, chat: bool, thinking: bool,
                     deep_research: bool, extract: bool, abstract: bool, analyze: bool):
        model_info = ModelInfoModel(
            show_name=show_name, model_name=model_name, describe=describe, url=url, apikey=apikey, model_type=model_type,
            max_input_tokens=max_input_tokens, batch_size=batch_size, function_calling=function_calling,
            json_output=json_output, structured_output=structured_output, concurrency=concurrency, extra=extra,
            chat=chat, thinking=thinking, deep_research=deep_research, extract=extract, abstract=abstract,
            analyze=analyze)
        g.session.add(model_info)
        await g.session.flush()

        return model_info.id

    @staticmethod
    async def update(model_id: int, show_name: str = None, model_name: str = None, describe: str = None, url: str = None,
                     apikey: str = None, model_type: str = None, max_input_tokens: int = None, batch_size: int = None,
                     function_calling: bool = None, json_output: bool = None, structured_output: bool = None,
                     concurrency: int = None, extra: dict = None, chat: bool = None, thinking: bool = None,
                     deep_research: bool = None, extract: bool = None, abstract: bool = None, analyze: bool = None):
        update_info = {}
        if show_name is not None:
            update_info[ModelInfoModel.show_name] = show_name
        if model_name is not None:
            update_info[ModelInfoModel.model_name] = model_name
        if describe is not None:
            update_info[ModelInfoModel.describe] = describe
        if url is not None:
            update_info[ModelInfoModel.url] = url
        if apikey is not None:
            update_info[ModelInfoModel.apikey] = apikey
        if model_type is not None:
            update_info[ModelInfoModel.model_type] = model_type
        if max_input_tokens is not None:
            update_info[ModelInfoModel.max_input_tokens] = max_input_tokens
        if batch_size is not None:
            update_info[ModelInfoModel.batch_size] = batch_size
        if function_calling is not None:
            update_info[ModelInfoModel.function_calling] = function_calling
        if json_output is not None:
            update_info[ModelInfoModel.json_output] = json_output
        if structured_output is not None:
            update_info[ModelInfoModel.structured_output] = structured_output
        if concurrency is not None:
            update_info[ModelInfoModel.concurrency] = concurrency
        if extra is not None:
            update_info[ModelInfoModel.extra] = extra
        if chat is not None:
            update_info[ModelInfoModel.chat] = chat
        if thinking is not None:
            update_info[ModelInfoModel.thinking] = thinking
        if deep_research is not None:
            update_info[ModelInfoModel.deep_research] = deep_research
        if extract is not None:
            update_info[ModelInfoModel.extract] = extract
        if abstract is not None:
            update_info[ModelInfoModel.abstract] = abstract
        if analyze is not None:
            update_info[ModelInfoModel.analyze] = analyze

        query = (update(ModelInfoModel)
                 .where(ModelInfoModel.id == model_id)
                 .values(update_info))
        await g.session.execute(query)

    @staticmethod
    def init_sync():
        with session_maker_sync() as session:
            ds_v3 = ModelInfoModel(
                id=1, show_name="DeepSeek-V3 250324", model_name=LLMModel.DEEPSEEK_V3_1_0821, describe="", url="", apikey="",
                model_type=ModelType.llm, max_input_tokens=32768, batch_size=1, function_calling=True, json_output=True,
                structured_output=True, concurrency=1, extra={}, chat=True, thinking=False, deep_research=False,
                extract=True, abstract=True, analyze=True)

            ds_r1 = ModelInfoModel(
                id=2, show_name="DeepSeek-Reasoner 250528", model_name=LLMModel.DEEPSEEK_V3_1_0821_THINKING, describe="", url="",
                apikey="", model_type=ModelType.llm, max_input_tokens=32768, batch_size=1, function_calling=True,
                json_output=True, structured_output=True, concurrency=1, extra={}, chat=True, thinking=True,
                deep_research=False, extract=True, abstract=False, analyze=False, icon="deepseek")

            # Qwen3 models have 32K context length, so max_input_tokens = 16K (half)
            qwen3_30b = ModelInfoModel(
                id=DEFAULT_QWEN_30B_MODEL_ID, show_name="Qwen3-30B-A3B-Instruct-2507", model_name=LLMModel.QWEN3_30B_INSTRUCT,
                describe="", url="", apikey="", model_type=ModelType.llm, max_input_tokens=16384, batch_size=1,
                function_calling=True, json_output=True, structured_output=True, concurrency=1, extra={}, chat=True,
                thinking=True, deep_research=False, extract=False, abstract=False, analyze=False, icon="qwen")

            gemini_2_5_flash = ModelInfoModel(
                id=5, show_name="Gemini 2.5 Flash", model_name=LLMModel.GEMINI_2_5_FLASH,
                describe="", url="", apikey="", model_type=ModelType.llm, max_input_tokens=1048576, batch_size=1,
                function_calling=True, json_output=True, structured_output=True, concurrency=1, extra={}, chat=True,
                thinking=True, deep_research=True, extract=True, abstract=True, analyze=True, icon="gemini")

            bge_m3 = ModelInfoModel(
                id=DEFAULT_BGE_M3_MODEL_ID, show_name="BGE-M3", model_name=EmbeddingModel.BGE_M3,
                describe="", url="", apikey="", model_type=ModelType.embedding, max_input_tokens=1024, batch_size=10,
                function_calling=False, json_output=False, structured_output=False, concurrency=1, extra={}, chat=False,
                thinking=False, deep_research=False, extract=False, abstract=False, analyze=False)

            bge_v2_m3 = ModelInfoModel(
                id=DEFAULT_BGE_RERANKER_V2_M3_MODEL_ID, show_name="BGE-RERANKER-V2-M3",  model_name=RerankerModel.BGE_RERANKER_V2_M3,
                describe="", url="", apikey="", model_type=ModelType.rerank, max_input_tokens=512, batch_size=10,
                function_calling=False, json_output=False, structured_output=False, concurrency=1, extra={}, chat=False,
                thinking=False, deep_research=False, extract=False, abstract=False, analyze=False)

            session.add_all([ds_v3, ds_r1, qwen3_30b, gemini_2_5_flash, bge_m3, bge_v2_m3])
            session.commit()


ModelInfo = ModelInfoController()
