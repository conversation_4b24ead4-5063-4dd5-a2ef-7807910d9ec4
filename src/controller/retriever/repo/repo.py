#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import uuid
from contextlib import ExitStack
from typing import List, Dict

from config import  EmbeddingModel
from engine.es import es
from common.logger import logger
from common.time import now_tz_datestring_with_millis
from model.doc import get_index
from controller.engine import EmbeddingEngine
from controller.retriever.base.base import BaseRetriever, async_time_cost
from controller.operator.chunking.base import RetrieveChunkModel, DocModel as LLMDoc_model
from controller.repository import Doc, ReferenceType


class RepoRetriever(BaseRetriever):
    """混合检索系统，结合BM25和向量检索方法进行文档搜索

    该类实现了一个混合检索系统，通过组合BM25关键词匹配和向量相似度计算两种方式
    对文档进行检索和排序，以提高检索的准确性和多样性。
    """

    def __init__(self,
                 query: str,
                 request_id: str = None,
                 repo_ids: List[int] = None,
                 doc_ids: List[int] = None,
                 custom_boost: List[dict] = None,
                 max_doc_size: int = 10,
                 doc_threshold: int = 20,
                 max_doc_chunk_size: int = None,
                 bm25_weight: float = 1,
                 embedding_weight: float = 1,
                 embedding_engine: EmbeddingEngine | None = EmbeddingEngine(EmbeddingModel.BGE_M3),
                 max_input_tokens: int = 8192,
                 topn: int = 20,
                 # 问题改写相关参数
                 query_rewrite: str = None,
                 query_keywords: List[str] | None = None,
                 query_associative_keywords: str | None = None,
                 query_search_terms: str | None = None,
                 query_keyword_weights: Dict[str, float] | None = None,
                 query_token_weights: Dict[str, float] | None = None):
        """构建混合检索
        todo: 时间序列的权重控制

        Args:
            request_id: 检索请求的唯一标识符，如果为None则自动生成
            repo_ids: 待检索的知识库ID列表
            doc_ids: 待检索的文档ID列表
            custom_boost: 文档召回的boost要求
            max_doc_size: 保留最多多少个文档筛选范围 必须非None
            max_doc_chunk_size: 每个文档保留最多多少片段数, None时自动计算
            bm25_weight: BM25检索结果的权重系数，默认为1
            embedding_weight: 向量检索结果的权重系数，默认为1
            embedding_engine: 文本向量化引擎实例
            topn: 最终最大片段数量

        """
        if bm25_weight < 0 or embedding_weight < 0 or (bm25_weight + embedding_weight) < 0:
            raise ValueError("bm25_weight/embedding_weight必须>=0, 且加和>0")
        if (not embedding_engine) and embedding_weight > 0:
            logger.warning("由于没有embedding_engine, embedding_weight将被强制等于0")

        request_id = request_id or str(uuid.uuid4())
        super().__init__(
            request_id=request_id,
            log_prefix=f"混合召回[{request_id}] ",
            bm25_weight=bm25_weight,
            custom_boost=custom_boost
        )
        self.query = query.strip()
        self.repo_ids = repo_ids
        self.doc_ids = doc_ids
        self.max_doc_size = max_doc_size
        self.doc_threshold = doc_threshold
        self.max_doc_chunk_size = max_doc_chunk_size
        self.max_input_tokens = max_input_tokens
        self.topn = topn
        self.index = get_index(repo_ids=repo_ids)  # 索引index范围

        # [embedding相关参数]
        self.embedding_engine = embedding_engine
        self.embedding_weight = embedding_weight if embedding_engine else 0
        self.query_vector: List[float] | None = None  # 问句向量,如果有query_rewrite则使用query_rewrite,否则使用query
        
        # [问题改写相关参数]
        self.query_rewrite = query_rewrite
        self.query_keywords = query_keywords
        self.query_associative_keywords = query_associative_keywords
        self.query_search_terms = query_search_terms
        self.query_keyword_weights = query_keyword_weights
        self.query_token_weights = query_token_weights

        # [记录字段] 方便外部取用
        self.filter_docs: List[dict] = []
        self.filter_doc_score: Dict[int, float] = {}
        self.search_repo_chunks: List[dict] = []
        self.repo_search_end_time: str | None = None

    @async_time_cost()
    async def searching(self) -> List[RetrieveChunkModel]:
        # 最测最终化器写入搜索完成时间
        with ExitStack() as cleanup:
            cleanup.callback(lambda: self._assign_search_end_time())

            # 文档问答,且长度小于模型接受的输入长度
            if (self.doc_ids
                    and (retrieve_chunks := await Doc.get_full_doc_retrieve(doc_ids=self.doc_ids))
                    and (sum([chunk.token_counts for chunk in retrieve_chunks]) <= self.max_input_tokens - 500)):
                return retrieve_chunks

            await self.embedding()
            doc_scores = await self.filter_doc()
            if not doc_scores:
                logger.info(self.log_prefix + f"文档未命中,返回空列表")
                return []

            source_chunks = await self.search(doc_scores=doc_scores)
            logger.info(self.log_prefix + f"知识库搜索成功 找到: {len(source_chunks)} 个文档片段")
            for chunk in source_chunks:
                self.search_repo_chunks.append({
                    "cid": chunk["cid"],
                    "filename": chunk["filename"],
                    "plain_text": chunk["plain_content"]
                })

            return [RetrieveChunkModel(**chunk) for chunk in source_chunks]

    @async_time_cost()
    async def searching_docs(self) -> List[LLMDoc_model]:
        doc_scores = await self.filter_doc(include=["doc_id", "filename", "html", "data_time", "extract_result"])
        self.repo_search_end_time = now_tz_datestring_with_millis()
        if not doc_scores:
            logger.info(self.log_prefix + f"文档未命中,返回空列表")
            return []

        return [LLMDoc_model(
            title=doc["filename"],
            content=doc["html"],
            data_time=doc["data_time"][:10],
            extract_result=doc.get("extract_result")) for doc in self.filter_docs][:self.max_doc_size]

    @async_time_cost()
    async def filter_doc(self, include: List[str] = None) -> dict:
        if include is None:
            include = ["doc_id", "filename", "repo_id"]

        dsl_query = {
            "bool": {
                "should": [
                    {
                        "function_score": {
                            "query": {
                                "bool": {
                                    "should": [
                                        *self.match_doc_query_keyword_weight()
                                    ] if self.query_keyword_weights else [
                                        *self.match_doc_query_token_weight(),
                                        # *self.match_doc_query_keyword(),
                                        # *self.match_doc_query_associative_keyword()
                                    ]
                                }
                            },
                            "functions": [
                                *self.function_score_doc_multiply(),
                                *self.function_score_doc_custom()
                            ],
                            "boost_mode": "multiply"
                        }
                    }
                ],
                "filter": self.build_doc_filters(),
            },
        }
        # logging.info(f"DSL：{json.dumps(dsl_query, ensure_ascii=False, indent=4)}")
        res = await es.search(
            index=self.index,
            query=dsl_query,
            source_includes=include,
            size=self.max_doc_size,
            ignore_unavailable=True,
            search_type="dfs_query_then_fetch"
        )
        # logging.warning(json.dumps(res.body, ensure_ascii=False, indent=4))

        doc_scores = {}
        for doc in res["hits"]["hits"]:
            # res = await es.explain(index=self.index, query=dsl_query, id=doc["_source"]["doc_id"])
            source = {
                **doc["_source"],
                "score":  doc["_score"] if isinstance(doc["_score"], float) else self._es_max_score
            }
            if not self.doc_ids:
                if source["score"] < self.doc_threshold:
                    continue
            doc_scores[source["doc_id"]] = source["score"]
            self.filter_docs.append(source)

        self.filter_doc_score = doc_scores
        logger.info(self.log_prefix + f"粗筛文档范围: {[doc_scores]}")
        return doc_scores

    async def search(self, doc_scores: dict[int, float]):
        conditions = []
        if self.bm25_weight > 0:
            conditions.extend(self.match_chunk_keyword())
            conditions.extend(self.match_chunk_associative_keyword())
        if self.embedding_weight > 0:
            if self.query_vector:
                conditions.extend(self.script_cosine_similarity())
            else:
                logger.warning("召回中向量未得到结果,已关闭该条件")

        inner_size = self.max_doc_chunk_size or self.topn // len(doc_scores)
        if inner_size > 100:
            inner_size = 100
        if inner_size < 5:
            inner_size = 5

        dsl = {
            "bool": {
                "should": [
                    {
                        "nested": {
                            "path": "chunks",
                            "score_mode": "max",
                            "query": {
                                "bool": {
                                    "should": [
                                        {
                                            "function_score": {
                                                "query": {
                                                    "bool": {
                                                        "should": conditions
                                                    }
                                                },
                                                "functions": [
                                                    *self.function_score_chunk_boost(doc_scores=doc_scores),
                                                    #*self.function_score_chunk_multiply(),
                                                    *self.function_score_chunk_custom(),
                                                ],
                                                "boost_mode": "multiply"
                                            },
                                        }
                                    ],
                                    "must_not": {
                                        "terms": {
                                            # ChunkType
                                            "chunks.type_": [
                                                "toc",
                                                "doc_title"
                                            ]
                                        }
                                    }
                                }
                            },
                            "inner_hits": {
                                "_source": [
                                    "chunks.cid",
                                    "chunks.type_",
                                    "chunks.title",
                                    "chunks.html_content",
                                    "chunks.plain_content",
                                    "chunks.xpath",
                                    "chunks.token_counts",
                                    "chunks.bboxes"
                                ],
                                # 每个文档几个片段
                                "size": inner_size
                            }
                        }
                    }
                ],
                "filter": [
                    {"terms": {"doc_id": [doc_id for doc_id in doc_scores.keys()]}}
                ],
                "minimum_should_match": 1
            }
        }

        res = await es.search(
            index=self.index,
            query=dsl,
            source_includes=[
                "doc_id",
                "filename",
                "data_time",
                "reference_type"
            ],
            size=self.max_doc_size,
            ignore_unavailable=True,
            search_type="dfs_query_then_fetch",
        )
        source_data = []
        for doc in res["hits"]["hits"]:
            # self.post_process_doc_annealing(hits=doc["inner_hits"]["chunks"]["hits"]["hits"])
            for i, chunk in enumerate(doc["inner_hits"]["chunks"]["hits"]["hits"]):
                # print(doc["_score"], window["_score"], window["_source"]["content"][:50].replace("\n", ""))
                if chunk["_score"] == "Infinity":
                    chunk["_score"] = self._es_max_score
                doc["_source"].setdefault("reference_type", ReferenceType.html)
                source_data.append(
                    {
                        **doc["_source"],
                        **chunk["_source"],
                        "score": chunk["_score"],
                        "web_search": False,
                    }
                )
        source_data = list(sorted(source_data, key=lambda x: x["score"], reverse=True))
        return source_data

    def build_doc_filters(self):
        filters = []
        if self.repo_ids is not None:
            filters.append({"terms": {"repo_id": self.repo_ids}})
        if self.doc_ids is not None:
            filters.append({"terms": {"doc_id": self.doc_ids}})
        return filters

    def script_cosine_similarity(self):
        """
        [分片筛选策略] Embedding策略,受embedding_weight影响权重
        Returns:

        """
        return [{
            "script_score": {
                "query": {
                    "bool": {
                        "must": [
                            {"match_all": {}},
                            {"exists": {"field": "chunks.vector"}}
                        ]
                    }
                },
                "script": {
                    "source": "Math.max(0, cosineSimilarity(params.query_vector, 'chunks.vector'))",
                    "params": {
                        "query_vector": self.query_vector
                    }
                },
                "boost": round(150 * self.embedding_weight, 2)
            }
        }]

    @async_time_cost()
    async def embedding(self):
        if self.embedding_weight > 0 and self.embedding_engine is not None:
            vectors = await self.embedding_engine.encode(text=[self.query_rewrite if self.query_rewrite else self.query])
            self.query_vector = vectors[0]

    @staticmethod
    async def es_tokens(query: str, analyzer: str, index: str = "repo_0"):
        ik_smart_response = await es.indices.analyze(
            index=index,
            body={
                "analyzer": analyzer,
                "text": query,
            },
        )
        tokens = [token["token"] for token in ik_smart_response["tokens"]]

        return tokens

    def _assign_search_end_time(self):
        self.repo_search_end_time = now_tz_datestring_with_millis()