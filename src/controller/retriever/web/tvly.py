#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from enum import StrE<PERSON>
from typing import Annotated, Optional, List

from tavily import AsyncTavilyClient
from pydantic import BaseModel, Field

from config import TVLY_APIKEY
from common.logger import logger
from controller.retriever.web.base import WebRetriever, SearchResult


class TvlySearchConfig(BaseModel):
    class SearchRecency(StrEnum):
        day = "day"
        week = "week"
        month = "month"
        year = "year"

    extract: Annotated[bool, Field(title="是否进行内容提取")] = False
    include_domains: Annotated[Optional[List[str]], Field(title="指定域名")] = None
    exclude_domains: Annotated[Optional[List[str]], Field(title="排除域名")] = None
    recency: Annotated[Optional[SearchRecency], Field(title="搜索时效")] = None
    count: Annotated[int, Field(title="返回结果数量", ge=1, le=20)] = 10


class TvlyWebRetriever(WebRetriever):
    async def web_search(self):
        score_threshold: float = 0.4
        try:
            tvly_client = AsyncTavilyClient(TVLY_APIKEY)
            response = await tvly_client.search(
                query=self.query_rewrite or self.query,
                max_results=self.search_config.count,
                include_domains=self.search_config.include_domains,
                exclude_domains=self.search_config.exclude_domains,
                time_range=self.search_config.recency
            )
        except Exception as e:
            logger.error(self.log_prefix + f"[Tavily]网络搜索失败: query: {self.query} error: {str(e)}")
            return []
        else:
            results = [SearchResult(
                title=sr["title"],
                content=sr["content"],
                url=sr["url"],
                score=sr["score"]
            ) for sr in response["results"] if sr["score"] >= score_threshold]
            logger.info(self.log_prefix + f"[Tavily]网络搜索成功: query: {self.query} 找到{len(results)}/{self.search_config.count}个相关结果")
            return results