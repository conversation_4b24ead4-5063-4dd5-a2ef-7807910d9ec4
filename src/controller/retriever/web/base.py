#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import uuid
import hashlib
import asyncio
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, model_validator

from common.time import now_tz_datestring_with_millis
from common.logger import logger
from engine.es import es
from model.doc import WEB_DOC_INDEX
from controller.parser import <PERSON><PERSON><PERSON><PERSON>, HtmlChunker
from controller.parser.chunker import BGE_M3_TOKENIZER
from controller.retriever.base import BaseRetriever
from controller.operator.chunking.base import RetrieveChunkModel, DocModel as LLMDocModel


class SearchResult(BaseModel):
    """解析出个标准格式"""
    doc_id: Optional[int] = Field(title="文档ID", default=None)
    content: str = Field(title="正文/摘要", default=None)
    url: str = Field(title="链接")
    source: Optional[str] = Field(title="来源", default=None)
    data_time: Optional[str] = Field(title="数据时间", default=None)
    title: str = Field(title="标题")
    score: Optional[float] = Field(title="相关性评分", default=1.0)
    icon: Optional[str] = Field(title="icon", default=None)

    @model_validator(mode="after")
    def complete_doc_id(self):
        if self.doc_id is None:
            # 使用 MD5 生成稳定的哈希值
            md5_hash = hashlib.sha256(self.url.encode("utf-8")).hexdigest()
            # 取前16位（64位）转为整数
            self.doc_id = int(md5_hash[:16], 16) + 1000000000  # 增加10亿防止和知识库冲突
        return self


class WebRetriever(BaseRetriever):
    def __init__(self,
                 query: str,
                 search_config: Any,
                 request_id: str = None,
                 max_doc_chunk_size: int = None,
                 topn: int = 10,
                 bm25_weight: float = 1,
                 custom_boost: List[dict] = None,
                 # 问题改写相关参数
                 query_rewrite: str = None,
                 query_keywords: List[str] | None = None,
                 query_associative_keywords: str | None = None,
                 query_search_terms: str | None = None,
                 query_keyword_weights: Dict[str, float] | None = None,
                 query_token_weights: Dict[str, float] | None = None):
        request_id = request_id or str(uuid.uuid4())
        super().__init__(
            request_id=request_id,
            log_prefix=f"网络搜索[{request_id}] ",
            bm25_weight=bm25_weight,
            custom_boost=custom_boost,
        )
        self.query = query.strip()
        self.search_config = search_config
        self.search_results: List[SearchResult] = []
        self.doc_ids = []

        self.max_doc_chunk_size = max_doc_chunk_size
        self.topn = topn
        self.index = WEB_DOC_INDEX

        # [问题改写相关参数]
        self.query_rewrite = query_rewrite
        self.query_keywords = query_keywords
        self.query_associative_keywords = query_associative_keywords
        self.query_search_terms = query_search_terms
        self.query_keyword_weights = query_keyword_weights
        self.query_token_weights = query_token_weights

        # [记录字段] 方便外部取用
        self.web_search_docs: List[dict] = []
        self.web_extract_start_time: str | None = None
        self.web_search_end_time: str | None = None


    async def searching(self) -> List[RetrieveChunkModel]:
        logger.info(self.log_prefix + f"开始网络召回: {self.query}")
        self.search_results = await self.web_search()
        self.web_search_docs = [{"url": sr.url, "title": sr.title, "icon": sr.icon} for sr in self.search_results]

        if not self.search_results:
            source_chunks = []
        else:
            if self.search_config.extract:
                self.web_extract_start_time = now_tz_datestring_with_millis()
                doc_scores = await self.parsing()
                if not self.doc_ids:
                    source_chunks = []
                else:
                    source_chunks = await self.search(doc_scores=doc_scores)
            else:
                source_chunks = await self.transforming()

        self.web_search_end_time = now_tz_datestring_with_millis()
        return [RetrieveChunkModel(**chunk) for chunk in source_chunks]

    async def searching_docs(self) -> List[LLMDocModel]:
        logger.info(self.log_prefix + f"开始网络召回: {self.query}")
        self.search_results = await self.web_search()
        self.web_search_docs = [{"url": sr.url, "title": sr.title} for sr in self.search_results]
        self.web_search_end_time = now_tz_datestring_with_millis()
        return [LLMDocModel(
            title=doc.title,
            content=doc.content,
            data_time=doc.data_time[:10] if doc.data_time else None)
                   for doc in self.search_results][:self.topn][:self.search_config.count]

    async def transforming(self):
        """
        切割纯文本,并使返回结构与ES召回相等
        Returns:

        """
        # 切片更小

        source_chunks = []
        for sr in self.search_results:
            html_body = "".join([f"<p>{p}</p>" for p in sr.content.split("\n")])
            chunker = HtmlChunker(tokenizer=BGE_M3_TOKENIZER, max_tokens=512, max_tolerance_tokens=1024)
            chunks = chunker.chunk(html_content=f"""<!DOCTYPE html><html><body>{html_body}</body></html>""")
            for i, chunk in enumerate(chunks):
                source_chunks.append(
                    {
                        "cid": f"{sr.doc_id}_{i}",
                        "url": sr.url,
                        "title": [],
                        "filename": sr.title,
                        "html_content": "".join([node.html_content for node in chunk.nodes]),
                        "plain_content": chunk.plain_content,
                        "data_time": sr.data_time,
                        "doc_id": sr.doc_id,
                        "xpath": [],
                        "token_counts": chunk.token_counts,
                        "web_search": True,
                        "icon": sr.icon,
                        # "score": 1 - 0.02 * i  # 每个排位降低0.02分
                    }
                )

        return source_chunks

    async def parsing(self):
        parse_runners = []
        doc_ids = []
        doc_scores = {}
        for sr in self.search_results:
            runner = WebParser(
                doc_id=sr.doc_id, title=sr.title, url=sr.url, content=sr.content, source=sr.source,
                data_time=sr.data_time, icon=sr.icon)
            parse_runners.append(runner.exec())
            doc_scores[runner.doc_id] = sr.score
            doc_ids.append(runner.doc_id)

        await asyncio.gather(*parse_runners)
        self.doc_ids = doc_ids

        return doc_scores

    async def search(self, doc_scores: dict[int, float]):
        conditions = []
        conditions.extend(self.match_chunk_keyword())
        conditions.extend(self.match_chunk_associative_keyword())

        inner_size = self.max_doc_chunk_size or self.topn // len(doc_scores)
        if inner_size > 100:
            inner_size = 100
        if inner_size < 2:
            inner_size = 2

        dsl = {
            "bool": {
                "should": [
                    {
                        "nested": {
                            "path": "chunks",
                            "score_mode": "max",
                            "query": {
                                "bool": {
                                    "should": [
                                        {
                                            "function_score": {
                                                "query": {
                                                    "bool": {
                                                        "should": conditions
                                                    }
                                                },
                                                "functions": [
                                                    *self.function_score_chunk_custom(),
                                                ],
                                                "boost_mode": "multiply"
                                            },
                                        }
                                    ],
                                    # "must_not": {"terms": {}}  不需要的类型等chunk内的条件
                                }
                            },
                            "inner_hits": {
                                "_source": [
                                    "chunks.cid",
                                    "chunks.title",
                                    "chunks.html_content",
                                    "chunks.plain_content",
                                    "chunks.xpath",
                                    "chunks.token_counts",
                                ],
                                # 每个文档几个片段
                                "size": inner_size
                            }
                        }
                    }
                ],
                "filter": [
                    {"terms": {"doc_id": [doc_id for doc_id in doc_scores.keys()]}}
                ],
                "minimum_should_match": 1
            }
        }

        res = await es.search(
            index=self.index,
            query=dsl,
            source_includes=[
                "doc_id",
                "filename",
                "data_time",
                "url",
                "icon"
            ],
            size=len(self.doc_ids),
            ignore_unavailable=True
        )
        source_data = []
        for doc in res["hits"]["hits"]:
            self.post_process_doc_annealing(hits=doc["inner_hits"]["chunks"]["hits"]["hits"])
            for i, chunk in enumerate(doc["inner_hits"]["chunks"]["hits"]["hits"]):
                # print(doc["_score"], window["_score"], window["_source"]["content"][:50].replace("\n", ""))
                if chunk["_score"] == "Infinity":
                    chunk["_score"] = self._es_max_score

                source_data.append(
                    {
                        **doc["_source"],
                        **chunk["_source"],
                        "score": chunk["_score"],
                        "web_search": True,
                    }
                )
        source_data = list(sorted(source_data, key=lambda x: x["score"], reverse=True))
        return source_data


    async def web_search(self) -> List[SearchResult]: ...
