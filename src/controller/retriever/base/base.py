#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import json
import os
import re
import time
import uuid
import math
import functools
from typing import Optional, Callable, Any, List, Dict

import jieba.analyse

from engine.es import es
from common.logger import logger
from controller.retriever.base.rewrite import RewriteEngine
from controller.engine import RerankEngine, ChatMessage
from controller.operator.chunking.base import RetrieveChunkModel


BASE_DIR = os.path.dirname(os.path.abspath(__file__))


def get_stopwords():
    stopwords_path = os.path.join(BASE_DIR, "stopwords.txt")
    with open(stopwords_path, mode="r", encoding="utf-8") as file:
        return [line.strip() for line in file.readlines() if line.strip()]


def get_synonyms():
    synonyms_path = os.path.join(BASE_DIR, "synonyms.json")
    with open(synonyms_path, mode="r", encoding="utf-8") as file:
        synonyms = json.load(fp=file)
    return synonyms


def get_vocab():
    vocab_path = os.path.join(BASE_DIR, "vocab.json")
    with open(vocab_path, mode="r", encoding="utf-8") as file:
        vocab = json.load(fp=file)
    return vocab

def async_time_cost(name: Optional[str] = None) -> Callable:
    """装饰器：计算函数执行时间并记录日志

    Args:
        name: 自定义日志名称，如果为None则使用方法名

    Returns:
        装饰器函数
    """

    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> Any:
            # 使用提供的名称或函数名
            log_name = name or func.__name__
            # 记录开始时间
            start_time = time.time()
            # 执行原函数
            result = await func(*args, **kwargs)
            # 计算耗时
            cost_time = time.time() - start_time
            # 记录日志
            if isinstance(retriever := args[0], BaseRetriever):
                logger.info(f"{retriever.log_prefix}{log_name} 耗时 {cost_time:.3f}s")
            else:
                logger.info(f"{log_name} 耗时 {cost_time:.3f}s")
            return result

        return wrapper

    return decorator


class BaseRetriever:
    """集中存放各个retriever所需的基础资源、策略、引擎管理等"""
    stopwords = get_stopwords()
    synonyms = get_synonyms()
    vocab = get_vocab()
    not_zh_pattern = re.compile(r'^[a-zA-Z0-9\s\W]+$')

    def __init__(self,
                 request_id: str | None = None,
                 rewrite_engine: RewriteEngine | None = None,
                 rerank_engine: RerankEngine | None = None,
                 topn: int = 20,
                 rerank_max_size: int = 30,
                 rerank_threshold: float = None,
                 log_prefix: str = "",
                 bm25_weight: int |float = 1,
                 custom_boost: List[Dict] = None):
        """
        Args:
            request_id: 检索请求的唯一标识符，如果为None则自动生成
            custom_boost: 文档召回的boost要求
            bm25_weight: BM25检索结果的权重系数，默认为1
            rewrite_engine: 问题改写引擎实例
            rerank_engine: 检索结果重排序引擎实例
            rerank_max_size: rerank engine放入的片段数,无rerank_engine则无效
            rerank_threshold: rerank engine的保留阈值,无rerank_engine则无效
            topn: 最终最大片段数量
        """
        # [通用配置]
        self.request_id = request_id or str(uuid.uuid4())
        self.log_prefix = log_prefix
        self.query: str | None = None  # 原始问句,retrieve开始时阶段补足
        self._es_max_score = 3.4028235e+38
        self.custom_boost: list[dict] | None = custom_boost
        self.topn = topn

        # [rewriting相关参数]
        self.rewrite_engine: RewriteEngine | None = rewrite_engine
        self.history: List[ChatMessage] | None = None  #  历史问答,retrieve开始时阶段补足
        self.query_rewrite: str | None = None  # 多轮改写问题,如果有rewrite_engine,在rewriting补足
        self.query_keywords: List[str] | None = None  # 问题关键词,如果有rewrite_engine,在rewriting补足
        self.query_associative_keywords: str | None = None  # 问题联想词,如果有rewrite_engine,在rewriting补足
        self.query_search_terms: str | None = None  # 问题扩展,如果有rewrite_engine,在rewriting补足
        self.query_keyword_weights: Dict[str, float] | None = None  # 基于rewrite_engine的关键词权重
        self.query_token_weights: Dict[str, float] | None = None  # 问句关键词及权重,如果有query_rewrite则使用query_rewrite,否则使用query


        self.bm25_weight = bm25_weight
        # 默认文档字段权重
        self.field_boost = {
            "filename": 2,
            "source": 2,
            "author": 2,
            "plain_text": 1,
        }

        # 默认分片字段权重
        self.chunk_field_boost = {
            "chunks.title": 2,
            "chunks.plain_content": 1,
        }

        # [rerank相关参数]
        self.rerank_engine = rerank_engine
        self.rerank_max_size = rerank_max_size
        self.rerank_max_tokens = 512
        self.rerank_batch_size = 16
        if rerank_threshold:
            self.rerank_threshold = rerank_threshold
        else:
            if self.rewrite_engine:
                self.rerank_threshold = 0.8
            else:
                self.rerank_threshold = 0.15

        # 最终结果
        self.chunks: List[RetrieveChunkModel] | None = []

    @async_time_cost()
    async def rewriting(self):
        if self.rewrite_engine is None:
            self.query_token_weights = await self.get_query_token_weight_no_rewrite()
        else:
            rewrite_res = await self.rewrite_engine.rewrite(query=self.query, history=self.history)
            logger.info(self.log_prefix+f"问题改写: {json.dumps(rewrite_res, ensure_ascii=False)}")

            self.query_rewrite = rewrite_res.get("query_rewrite")
            self.query_keywords = rewrite_res.get("query_keywords")
            if self.query_keywords:
                self.query_keywords = [kw for kw in self.query_keywords if kw in self.query_rewrite or kw in self.query]
            self.query_associative_keywords = rewrite_res.get("query_associative_keywords")
            self.query_search_terms = rewrite_res.get("query_search_terms")
            await self.get_query_token_weight_with_rewrite()

    async def searching(self) -> List[RetrieveChunkModel]: ...

    async def get_query_token_weight_no_rewrite(self, total: int = 10, threshold: float = 0.1):
        if self.query is None:
            raise ValueError("`get_query_token_weight`需要query参数处理完毕")
        token_weight = {}

        smart_tokens = await self.es_tokens(query=self.query, analyzer="ik_smart")

        # 每有一个重复的词,说明此词可能比较重要,权重加0.3
        for token in smart_tokens:
            if token in token_weight:
                token_weight[token] += 0.3
            else:
                token_weight[token] = 1

        # 使用max_word最细粒度分词
        max_word_tokens = await self.es_tokens(query=self.query, analyzer="ik_max_word")

        for token in max_word_tokens:
            # 如果最细粒度分词在停用词中,或者在词库却属于不利于召回的词性,不加入召回
            if token in self.stopwords:
                continue
            if token in self.vocab:
                # c:连词  p:介词  u:助词  o:拟声词  zg:低价值词?
                if self.vocab[token]["s"] in ("c", "p", "u", "o", "zg"):
                    continue

            # 如果max_word分词的词不在smart的分词中长度大于1,且词被某个已有的词完全包含,则权重等于长词
            if token not in token_weight:
                if len(token) > 1:
                    for tw in token_weight.keys():
                        if token in tw:
                            token_weight[token] = token_weight[tw]
                            break
                    else:
                        token_weight[token] = 0.3
                # 否则这个词仅给与比较低的分数
                else:
                    token_weight[token] = 0.3

        # 使用jieba关键词,如果关键词和已提取词有匹配,则对该词的权重乘以平滑处理的关键词权重
        keywords = jieba.analyse.extract_tags(self.query, topK=5, withWeight=True)
        for kw, weight in keywords:
            if kw in token_weight and kw not in self.stopwords:
                token_weight[kw] *= (1 + weight * 0.2)

        # 对一个字的分词，太容易干扰，降低权重为原来的1/3
        for word, weight in list(token_weight.items()):
            if len(word) == 1:
                token_weight[word] = weight / 3

        for word, weight in list(token_weight.items()):
            if word not in self.synonyms:
                continue
            for synonym_word in self.synonyms[word]:
                if synonym_word in self.stopwords:
                    continue
                if synonym_word in token_weight:
                    token_weight[synonym_word] += weight * 0.1
                else:
                    token_weight[synonym_word] = weight * 0.1

        token_weight = {k: round(v, 2) for k, v in token_weight.items() if round(v, 2) >= threshold}
        if token_weight:
            # 压缩总分到total
            factor = total / sum(token_weight.values())
            token_weight = {k: round(v * factor, 2) for k, v in token_weight.items()}
            token_weight = dict(sorted(token_weight.items(), key=lambda item: item[1]))
        logger.info(self.log_prefix + f"问句关键词权重: {json.dumps(token_weight, ensure_ascii=False)}")

        return token_weight

    @staticmethod
    async def es_tokens(query: str, analyzer: str, index: str = "repo_0"):
        ik_smart_response = await es.indices.analyze(
            index=index,
            body={
                "analyzer": analyzer,
                "text": query,
            },
        )
        tokens = [token["token"] for token in ik_smart_response["tokens"]]

        return tokens


    async def get_query_token_weight_with_rewrite(self, total: float = 10):
        """
        当有rewrite engine时，原始基于词库、jieba抽取关键词的方法不如扩展问句和关键词，因此需要优化方法

        Returns:

        """
        query_keyword_weights = {}

        if self.query_keywords:
            query_keywords = sorted(self.query_keywords, key=lambda x: len(x), reverse=True)
            for keyword in query_keywords:
                for exist_kw in query_keyword_weights:
                    if keyword in exist_kw:
                        break
                else:
                    query_keyword_weights.setdefault(keyword, 0)
                    query_keyword_weights[keyword] += 2

        if self.query_associative_keywords:
            for keyword in self.query_associative_keywords:
                query_keyword_weights.setdefault(keyword, 0)
                query_keyword_weights[keyword] += 1

        if self.query_search_terms:
            for term in self.query_search_terms:
                for kw in query_keyword_weights:
                    if kw in term:
                        query_keyword_weights[kw] += 1

        for k1 in query_keyword_weights.keys():
            for k2, v in query_keyword_weights.items():
                if k2 in k1 and k2 != k1:
                    query_keyword_weights[k1] += v * 0.5
                    query_keyword_weights[k1] = round(query_keyword_weights[k1], 2)
                    query_keyword_weights[k2] = 0
        query_keyword_weights = {k: v for k, v in query_keyword_weights.items() if v != 0}
        if query_keyword_weights:
            # 压缩总分到total
            factor = total / sum(query_keyword_weights.values())
            query_keyword_weights = {k: round(v * factor, 2) for k, v in query_keyword_weights.items()}

        self.query_keyword_weights = dict(sorted(query_keyword_weights.items(), key=lambda item: item[1], reverse=True))
        logger.info(self.log_prefix + f"问句关键词权重: {json.dumps(query_keyword_weights, ensure_ascii=False)}")

        query_token_weights = {}
        if self.query_keyword_weights:
            for k, v in query_keyword_weights.items():
                smart_tokens = await self.es_tokens(query=k, analyzer="ik_smart")
                for token in smart_tokens:
                    query_token_weights.setdefault(token, 0)
                    query_token_weights[token] += round(float(v / len(smart_tokens)), 2)
        if query_keyword_weights:
            # 压缩总分到total
            factor = total / sum(query_token_weights.values())
            query_token_weights = {k: round(v * factor, 2) for k, v in query_token_weights.items()}

        self.query_token_weights = dict(sorted(query_token_weights.items(), key=lambda item: item[1], reverse=True))
        logger.info(self.log_prefix + f"问句Token权重: {json.dumps(query_token_weights, ensure_ascii=False)}")

    def match_doc_query_keyword_weight(self):
        """
        [文档筛选策略]基于rewrite得到的关键词权重,合并后进行召回

        Returns:

        """
        if not self.query_keyword_weights:
            return []

        keyword_condition = [{
            "multi_match": {
                "query": k,
                "fields": [f"{field}^{round(ratio * v, 2)}" for field, ratio in self.field_boost.items()],
                "type": "best_fields",
                "tie_breaker": 0.7
            }
        } for k, v in self.query_keyword_weights.items()]

        return keyword_condition

    def match_doc_query_token_weight(self):
        """
        [文档筛选策略]根据问句、关键词、联想词等切分出的词和权重,以bm25匹配文档的策略
        Args:

        Returns:

        """
        # 否则使用token词,一般来自于没有rewrite的情况
        if len(self.query_token_weights) >= 3:
            keyword_condition = [{
                "term": {
                    field: {
                        "value": t,
                        "boost": round(b * ratio, 1)
                    }
                }
            } for t, b in self.query_token_weights.items() for field, ratio in self.field_boost.items()]
        # 最后，使用问句作为兜底
        else:
            keyword_condition = [{
                "multi_match": {
                    "query": self.query_rewrite or self.query,
                    "fields": [f"{field}^{ratio}" for field, ratio in self.field_boost.items()],
                    "type": "best_fields",
                    "tie_breaker": 0.7
                }
            }]
        return keyword_condition

    def match_doc_query_keyword(self):
        """
        [文档筛选策略]根据大模型推理的问句关键词,BM25匹配最相关文档

        Returns:

        """
        if self.query_keywords is None:
            return []

        keyword_condition = [
            *[
                {
                    "multi_match": {
                        "query": kw,
                        "fields": [f"{field}^{ratio}" for field, ratio in self.field_boost.items()],
                        "type": "best_fields",
                        "tie_breaker": 0.7
                    }
                } for kw in self.query_keywords
            ],
            *[
                {
                    "multi_match": {
                        "query": kw,
                        "fields": [f"{field}^{ratio}" for field, ratio in self.field_boost.items()],
                        # "type": "phrase",
                        "analyzer": "ik_max_word",
                        "tie_breaker": 1.0
                    }
                } for kw in self.query_keywords
            ],
        ]

        return keyword_condition

    def match_doc_query_associative_keyword(self):
        """
        [文档筛选策略]根据大模型推理的问题联想词,BM25匹配最相关文档

        Returns:

        """
        if self.query_associative_keywords is None:
            return []

        associative_keyword_condition = [
            *[
                {
                    "multi_match": {
                        "query": kw,
                        "fields": [f"{field}^{ratio}" for field, ratio in self.field_boost.items()],
                        "type": "best_fields",
                        "tie_breaker": 0.7
                    }
                } for kw in self.query_associative_keywords
            ],
            *[
                {
                    "multi_match": {
                        "query": kw,
                        "fields": [f"{field}^{ratio}" for field, ratio in self.field_boost.items()],
                        # "type": "phrase",
                        "analyzer": "ik_max_word",
                        "tie_breaker": 1.0
                    }
                } for kw in self.query_associative_keywords
            ],
        ]
        return associative_keyword_condition

    def function_score_doc_multiply(self, power: float = 1.2, topn: int = 7):
        """
        [文档筛选策略]根据问句切分出的词和权重,对权重>0.5的词，在命中时累加权重，以实现更多的词命中，而非单一词命中足够多的目的
        Args:
            power:
            topn:
        Returns:

        """
        if self.query_keyword_weights:
            multiply_tokens = [word for word, weight in self.query_keyword_weights.items() if weight > 0.5]

            return [
                {
                    "filter": {
                        "match_phrase": {f"plain_text": tk}
                    },
                    "weight": power
                } for tk in multiply_tokens
            ][:topn]
        else:
            multiply_tokens = [word for word, weight in self.query_token_weights.items() if weight > 0.5]
            return [
                {
                    "filter": {
                        "term": {f"plain_text": tk}
                    },
                    "weight": power
                } for tk in multiply_tokens
            ][:topn]

    def function_score_doc_custom(self):
        return [boost for boost in self.custom_boost if "chunks." not in str(boost)] if self.custom_boost else []

    def match_chunk_keyword(self):
        """
        [分片筛选策略] 根据问句、关键词、联想词等切分出的词和权重,以bm25匹配文档的策略
        Returns:

        """
        if self.query_keyword_weights and len(self.query_keywords) >= 2:
            keyword_condition = []
            for t, b in self.query_keyword_weights.items():
                # 不包含中文,说明分词很可能不在ik词表中,未被良好分词
                if self.not_zh_pattern.match(t) and len(t) > 1:
                    # todo: ngram切换
                    keyword_condition.append(
                        {
                            "multi_match": {
                                "query": t,
                                "fields": [f"{chunk_field}^{round(ratio*b, 2)}" for chunk_field, ratio in self.chunk_field_boost.items()],
                                "type": "best_fields",
                                "tie_breaker": 1.0,
                                "boost": self.bm25_weight * 1.25
                            }
                        }
                    )
                else:
                    keyword_condition.append(
                        {
                            "multi_match": {
                                "query": t,
                                "fields": [f"{chunk_field}^{round(ratio*b, 2)}" for chunk_field, ratio in self.chunk_field_boost.items()],
                                "type": "best_fields",
                                "tie_breaker": 0.7,
                                "boost": self.bm25_weight
                            }
                        }
                    )
                keyword_condition.append({
                    "multi_match": {
                        "query": t,
                        "fields": [f"{chunk_field}^{round(ratio*b, 2)}" for chunk_field, ratio in self.chunk_field_boost.items()],
                        "type": "phrase",
                        "analyzer": "ik_max_word",
                        "tie_breaker": 1.0,
                        "boost": self.bm25_weight
                    }
                })
        else:
            keyword_condition = [{
                "multi_match": {
                    "query": self.query,
                    "fields": [f"{chunk_field}^{ratio}" for chunk_field, ratio in self.chunk_field_boost.items()],
                    "type": "best_fields",
                    "tie_breaker": 0.7,
                    "boost": self.bm25_weight
                }
            }]

        return keyword_condition

    def match_chunk_query_keyword(self):
        """
        [分片筛选策略] 根据大模型提取的关键词,以bm25匹配段落的策略

        Returns:

        """
        if self.query_keywords is None:
            return []

        keyword_condition = [
            *[
                {
                    "multi_match": {
                        "query": kw,
                        "fields": [f"{field}^{ratio}" for field, ratio in self.chunk_field_boost.items()],
                        "type": "best_fields",
                        "tie_breaker": 0.7,
                        "boost": self.bm25_weight * 0.5
                    }
                } for kw in self.query_keywords
            ],
            *[
                {
                    "multi_match": {
                        "query": kw,
                        "fields": [f"{field}^{ratio}" for field, ratio in self.chunk_field_boost.items()],
                        "type": "phrase",
                        "analyzer": "ik_max_word",
                        "tie_breaker": 1.0
                    }
                } for kw in self.query_keywords
            ]
        ]

        return keyword_condition

    def match_chunk_associative_keyword(self):
        """
        [分片筛选策略] 根据大模型提取的联想词，以bm25匹配段落的策略
        Returns:

        """
        if self.query_associative_keywords is None:
            return []

        associated_condition = [
            *[
                {
                    "multi_match": {
                        "query": kw,
                        "type": "best_fields",
                        "fields": [f"{chunk_field}^{ratio}" for chunk_field, ratio in self.chunk_field_boost.items()],
                        "boost": self.bm25_weight
                    }
                } for kw in self.query_associative_keywords
            ],
            *[
                {
                    "multi_match": {
                        "query": kw,
                        "type": "phrase",
                        "fields": [f"{chunk_field}^{ratio}" for chunk_field, ratio in self.chunk_field_boost.items()],
                        "boost": self.bm25_weight
                    }
                } for kw in self.query_associative_keywords
            ],
        ]
        return associated_condition

    def function_score_chunk_boost(self, doc_scores: dict, _min: (int, float) = 1, _max: (int, float) = 1.2):
        """压缩分数到[_min, _max]"""
        if not doc_scores:
            return []
        max_value = max(doc_scores.values())
        min_value = min(doc_scores.values())

        normalized_doc_scores = {
            doc_id: round(1 + (score - min_value) * (_max - _min) / (max_value - min_value), 3)
            if max_value > min_value
            else 1
            for doc_id, score in doc_scores.items()
        }
        logger.info(self.log_prefix + f"normalized_doc_scores: {normalized_doc_scores}")
        function_score_normalized = [
            {
                "filter": {
                    "prefix": {f"chunks.cid": f"{k}_"}
                },
                "weight": v
            } for k, v in normalized_doc_scores.items()
        ]
        return function_score_normalized

    def function_score_chunk_multiply(self) -> list[dict] | None:
        return [
            {
                "filter": {
                    "multi_match": {
                        "query": k,
                        "type": "phrase",
                        "fields": ["chunks.plain_content"],
                        "tie_breaker": 1.0,
                    }
                },
                "weight": min(1.1 if v < 1.1 else v, 2)  # [1.1, 2]
            } for k, v in self.query_keyword_weights.items()
        ]

    def function_score_chunk_custom(self):
        return [boost for boost in self.custom_boost if "chunks." in str(boost)] if self.custom_boost else []

    @staticmethod
    def post_process_doc_annealing(hits: list):
        """对同一个文档的多个区间进行退火"""
        doc_score_annealing_factor = 0.96

        for i, sr in enumerate(hits):
            sr["_score"] = round(sr["_score"] * doc_score_annealing_factor ** i, 4)
