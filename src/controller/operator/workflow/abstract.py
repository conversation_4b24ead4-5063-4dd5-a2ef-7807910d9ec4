import re
from typing import List

from pydantic import BaseModel

from config import DEFAULT_DOC_CHUNK_SIZE, DEFAULT_ABSTRACT_CHUNK_SIZE, DEFAULT_ABSTRACT_OVERLAP_SIZE, LLMModel
from controller.operator.chunking import DocModel, DocChunking, SuggestChunking
from controller.operator.runner.abstract import AbstractSuggest, AbstractGenerator, AbstractCombine, SuggestFormatFix
from controller.operator.workflow import BaseWorkflow


class AbstractWorkflowModel(BaseModel):
    AbstractSuggestModel: LLMModel = LLMModel.DEEPSEEK_V3_1_0821_THINKING
    AbstractGeneratorModel: LLMModel = LLMModel.DEEPSEEK_V3_1_0821_THINKING
    AbstractCombineModel: LLMModel = LLMModel.DEEPSEEK_V3_1_0821_THINKING
    SuggestFormatFixModel: LLMModel = LLMModel.DEEPSEEK_V3_1_0821


class AbstractWorkflow(BaseWorkflow):
    def __init__(self, doc_list: List[DocModel], user_prompt: str, doc_chunk_size: int = DEFAULT_DOC_CHUNK_SIZE,
                 model: AbstractWorkflowModel = AbstractWorkflowModel()):
        super().__init__()

        self.doc_chunking = DocChunking(chunk_size=doc_chunk_size)
        self.suggest_chunking = SuggestChunking(chunk_size=DEFAULT_ABSTRACT_CHUNK_SIZE,
                                                overlap_size=DEFAULT_ABSTRACT_OVERLAP_SIZE)
        self.doc_list = doc_list
        self.user_prompt = user_prompt
        self.model = model

    @staticmethod
    def extract_suggest_numbers(text: str) -> List[int]:
        pattern = r'\[suggest:(\d+)\]'
        numbers = re.findall(pattern, text)
        numbers = list(map(int, numbers))
        return numbers

    async def _run(self):
        chunk_result = self.doc_chunking.chunk(self.doc_list)
        runners = [AbstractSuggest(
            user_prompt=self.user_prompt,
            chunk=chunk,
            token_consumption=self.token_consumption,
            model_name=self.model.AbstractSuggestModel
        ) for chunk in chunk_result]
        abstract_suggest_result = await self.run_with_concurrency(runners)

        abstract_suggest_list = [item.model_dump() for sublist in abstract_suggest_result for item in sublist.abstract_suggest_list]

        suggest_index = {}
        for index, item in enumerate(abstract_suggest_list):
            suggest_index[index] = item.pop("citation_list")
            item["suggest_index"] = index

        if len(f"{abstract_suggest_list}") > DEFAULT_ABSTRACT_CHUNK_SIZE:
            abstract_suggest_chunk_list = self.suggest_chunking.chunk(abstract_suggest_list)

            runners = [AbstractGenerator(
                user_prompt=self.user_prompt,
                abstract_suggest_list=abstract_suggest,
                token_consumption=self.token_consumption,
                model_name=self.model.AbstractGeneratorModel
            ) for abstract_suggest in abstract_suggest_chunk_list]

            abstract_suggest_list = await self.run_with_concurrency(runners)
            generate_attach_response = await AbstractCombine(
                user_prompt=self.user_prompt,
                abstract_list=abstract_suggest_list,
                token_consumption=self.token_consumption,
                model_name=self.model.AbstractCombineModel
            ).run()

        else:
            generate_attach_response = await AbstractGenerator(user_prompt=self.user_prompt,
                                                               abstract_suggest_list=abstract_suggest_list,
                                                               token_consumption=self.token_consumption,
                                                               model_name=self.model.AbstractGeneratorModel).run()

        suggest_format_fix_response = await SuggestFormatFix(content=generate_attach_response,
                                                             token_consumption=self.token_consumption,
                                                             model_name=self.model.SuggestFormatFixModel).run()

        suggest_list = self.extract_suggest_numbers(text=suggest_format_fix_response)
        for suggest_id in suggest_list:
            if suggest_id in suggest_index:
                citation_str = "".join([f"[citation:{index}]" for index in suggest_index[suggest_id]])
                suggest_format_fix_response = suggest_format_fix_response.replace(f"[suggest:{suggest_id}]", citation_str)
            else:
                suggest_format_fix_response = suggest_format_fix_response.replace(f"[suggest:{suggest_id}]", "")

        return suggest_format_fix_response
