from typing import List

from openai import AsyncStream
from openai.types.chat import ChatCompletionChunk

from config import LLMModel
from controller.engine import ChatEngine
from controller.operator.chunking import RetrieveChunkModel
from controller.operator.prompt.doc_qa import doc_qa_prompt
from controller.operator.runner.base import BaseRunner, TokenConsumption
from controller.operator.tool.time import get_current_time
from model.session import ChatMessage


class DocQA(BaseRunner):
    def __init__(self, chunks: List[RetrieveChunkModel], user: str, history: List[ChatMessage] = None,
                 model_name: str | LLMModel = LLMModel.DEEPSEEK_V3_1_0821, token_consumption: TokenConsumption | None = None):
        super().__init__()

        self.chunks = chunks
        self.user = user
        self.history = history
        self.token_consumption = token_consumption or TokenConsumption()
        self.model_engine = ChatEngine(model_name=model_name, token_consumption=self.token_consumption)

        if "deepseek-r" in model_name.lower() or "deepseek-reason" in model_name.lower():
            thinking_prompt = "4. 在思考过程中，如果你需要从提供的文档里查找相关的信息，需要说明文档对应的名称/标题，并严格遵守引用格式，禁止在思考过程中使用 `ref_id`。\n"
        else:
            thinking_prompt = None

        # 索引变换修改到内层
        search_result = [{
            "ref_id": i, **chunk.llm_output()
        } for i, chunk in enumerate(self.chunks)]
        self.system_prompt = doc_qa_prompt.format(
            current_time=get_current_time(),
            search_results=search_result,
            thinking_prompt=thinking_prompt,
        )

    async def _run(self) -> AsyncStream[ChatCompletionChunk]:
        response = await self.model_engine.generator(
            prompt=self.user,
            system_prompt=self.system_prompt,
            history=self.history,
            stream=True
        )
        return response
