import asyncio
import json

from pydantic import Field
from typing import Annotated, List

from config import LLMModel
from controller.engine import Chat<PERSON><PERSON><PERSON>
from controller.operator.prompt.doc_qa import generate_title_prompt
from controller.operator.runner.base import BaseRunner, TokenConsumption
from model.session import ChatMessage


class GenerateTitle(BaseRunner):
    def __init__(self, chat_history: List[ChatMessage | dict],
                 model_name: LLMModel = LLMModel.DEEPSEEK_V3_1_0821, token_consumption: TokenConsumption | None = None):
        super().__init__()

        self.chat_history: Annotated[List[ChatMessage | dict], Field(description="对话历史记录")] = chat_history
        self.token_consumption = token_consumption or TokenConsumption()
        self.model_engine = ChatEngine(model_name=model_name, token_consumption=self.token_consumption)

    async def _run(self) -> str:
        chat_history = []
        for item in self.chat_history:
            if isinstance(item, ChatMessage):
                if item.user:
                    chat_history.append({"role": "user", "content": item.user})
                if item.assistant:
                    chat_history.append({"role": "assistant", "content": item.assistant})
            else:
                chat_history.append(item)

        response = await self.model_engine.generator(
            system_prompt=generate_title_prompt,
            prompt=f"{json.dumps(chat_history, ensure_ascii=False)}",
        )
        return response.choices[0].message.content.strip()


if __name__ == '__main__':
    runner = GenerateTitle(chat_history=[{"user": "你好，今天的天气怎么样？"}])
    result = asyncio.run(runner.run())
    print(result)  # 输出生成的标题
