from config import LLMModel
from controller.engine import ChatEngine
from controller.operator.prompt.abstract import suggest_format_fix_prompt
from controller.operator.runner.base import BaseRunner, TokenConsumption


class SuggestFormatFix(BaseRunner):
    def __init__(self, content: str, model_name: LLMModel = LLMModel.DEEPSEEK_V3_1_0821,
                 token_consumption: TokenConsumption | None = None):
        super().__init__()

        self.content = content
        self.token_consumption = token_consumption or TokenConsumption()
        self.model_engine = ChatEngine(model_name=model_name, token_consumption=self.token_consumption)

    async def _run(self) -> str:
        thinking_response = await self.model_engine.generator(
            system_prompt=suggest_format_fix_prompt,
            prompt=f"文本内容：{self.content}",
        )
        content = thinking_response.choices[0].message.content.strip()

        return content
