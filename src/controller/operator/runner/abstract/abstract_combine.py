from typing import List

from config import LLMModel
from controller.engine import ChatEngine
from controller.operator.prompt.abstract import abstract_combine_prompt
from controller.operator.runner.base import BaseRunner, TokenConsumption


class AbstractCombine(BaseRunner):
    def __init__(self, user_prompt: str, abstract_list: List[dict],
                 model_name: LLMModel = LLMModel.DEEPSEEK_V3_1_0821_THINKING, token_consumption: TokenConsumption | None = None):
        super().__init__()

        self.user_prompt = user_prompt
        self.abstract_list = abstract_list
        self.token_consumption = token_consumption or TokenConsumption()
        self.model_engine = ChatEngine(model_name=model_name, token_consumption=self.token_consumption)

    async def _run(self):
        thinking_response = await self.model_engine.generator(
            system_prompt=abstract_combine_prompt.format(
                user_prompt=self.user_prompt,
            ),
            prompt=f"摘要列表：{self.abstract_list}",
        )
        content = thinking_response.choices[0].message.content.strip()

        return content
