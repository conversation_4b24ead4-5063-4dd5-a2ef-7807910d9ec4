from typing import List

from config import LLMModel
from controller.engine import ChatEngine
from controller.operator.prompt.doc_extract import combine_extract_prompt
from controller.operator.runner.base import BaseRunner, TokenConsumption


class CombineAbstract(BaseRunner):
    def __init__(self, content_list: List[str], model_name: LLMModel = LLMModel.DEEPSEEK_V3_1_0821,
                 token_consumption: TokenConsumption | None = None):
        super().__init__()

        self.content_list = content_list
        self.token_consumption = token_consumption or TokenConsumption()
        self.model_engine = ChatEngine(model_name=model_name, token_consumption=self.token_consumption)

    async def _run(self) -> str:
        response = await self.model_engine.generator(
            system_prompt=combine_extract_prompt,
            prompt=f"{self.content_list}",
        )
        content = response.choices[0].message.content.strip()
        return content


if __name__ == '__main__':
    import asyncio

    async def main():
        from controller.operator.example_text import short_text

        results = await asyncio.gather(*[
            CombineAbstract(content_list=[short_text, short_text], model_name=LLMModel.QWEN3_32B).run()
            for _ in range(1)
        ])
        for content in results:
            print("--" * 20)
            print(content)

    asyncio.run(main())
