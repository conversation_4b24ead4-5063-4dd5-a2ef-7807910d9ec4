from autogen_core.models import Chat<PERSON>ompletion<PERSON>lient

from controller.engine import deepseek_v3_0324
from controller.operator.agent import BaseAgent
from controller.operator.runner.base import TokenConsumption
from controller.operator.tool.time import get_current_time
from controller.operator.prompt.deep_research.coordinator import coordinator_prompt


async def planner() -> str:
    return "Start planning tasks for the agents, end with TERMINATE when done."


class CoordinatorAgent(BaseAgent):
    def __init__(self, model_client: ChatCompletionClient = deepseek_v3_0324, token_consumption=None):
        self.token_consumption = token_consumption or TokenConsumption()

        super().__init__(
            name="coordinator",
            model_client=model_client,
            token_consumption=token_consumption,
            description="A coordinator agent that manages and delegates tasks to other agents.",
            system_message=coordinator_prompt.format(current_time=get_current_time()),
            tools=[planner]
        )


if __name__ == '__main__':
    import asyncio
    from typing import Sequence

    from autogen_agentchat.base import Response
    from autogen_agentchat.messages import TextMessage, BaseChatMessage, ToolCallExecutionEvent, ToolCallRequestEvent
    from autogen_core import CancellationToken
    from autogen_core.models import ChatCompletionClient

    agent = CoordinatorAgent()
    messages: Sequence[BaseChatMessage] = [
        TextMessage(source="user", content="你是谁"),
    ]
    cancellation_token = CancellationToken()

    async def get_streaming_response():
        async for message in agent.on_messages_stream(messages, cancellation_token):
            if isinstance(message, TextMessage):
                print("text:", message.content)
            elif isinstance(message, ToolCallRequestEvent):
                print("tool call request:", message)
            elif isinstance(message, ToolCallExecutionEvent):
                print("tool call execution:", message)
            elif isinstance(message, Response):
                print("response:", message)
            else:
                print("unknown message type:", message)

    response = asyncio.run(get_streaming_response())
