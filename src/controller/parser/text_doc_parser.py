#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from common.logger import logger
from common.time import now_tz_datestring
from common import g
from engine.es import es
from controller.parser.base import Parser<PERSON><PERSON>, Chunker
from controller.engine import EmbeddingEngine
from controller.repository import Doc, TextDoc, DocStatus, get_index, ReferenceType


class TextDocParser(ParserBase):
    def __init__(self,
                 doc_id: int,
                 text_doc: TextDoc,
                 chunker: Chunker,
                 embedding_engine: EmbeddingEngine = None,
                 embedding_batch_size: int = 8):
        super().__init__(doc_id=doc_id,
                         chunker=chunker,
                         reference_type=ReferenceType.html,
                         embedding_engine=embedding_engine,
                         embedding_batch_size = embedding_batch_size)
        self.text_doc: TextDoc = text_doc


    async def preparing(self):
        await self.load_doc()
        # 跳过基类默认下载阶段
        logger.info(f"{self.log_prefix}prepare阶段完成")

    async def parsing(self):
        await Doc.update(doc_id=self.doc_id, status=DocStatus.parsing)
        await g.session.commit()

        if self.text_doc.html_content:
            self.html = self.text_doc.html_content
        else:
            self.html = "\n".join(f"<p>{line}</p>" for line in self.text_doc.content.split("\n"))

    async def es_data_index(self):
        logger.info(f"{self.log_prefix}开始执行ES文档构建上传")
        chunks = [
            {
                "cid": f"{self.doc_id}_{i}",
                "type_": chunk.type_,
                "index": i,
                "title": chunk.title,
                "html_content": "\n".join([node.html_content for node in chunk.nodes]),
                "plain_content": chunk.plain_content,
                "xpath": [node.xpath for node in chunk.nodes if node.xpath],
                "token_counts": chunk.token_counts,
                "vector": chunk.vector,
                "start_offset": chunk.start_offset,
                "end_offset": chunk.end_offset,
            }
            for i, chunk in enumerate(self.chunks)
        ]
        index = get_index(repo_ids=[self.doc["repo_id"]])

        es_doc = {
            "doc_id": self.doc_id,
            "tenant_id": self.text_doc.tenant_id,
            "repo_id": self.text_doc.repo_id,
            "filename": self.text_doc.title,
            "reference_type": self.reference_type,
            "status": DocStatus.parse_success if self.error is None else DocStatus.parse_fail,
            "plain_text": self.plain_text,
            "tags": self.text_doc.tags,
            "title": self.text_doc.title,
            "source": self.text_doc.source,
            "url": self.text_doc.url,
            "author": self.text_doc.author,
            "html": self.html,
            "token_counts": sum([chunk.token_counts for _, chunk in enumerate(self.chunks) if chunk.token_counts]),
            "chunks": chunks,
            "keywords": self.keywords,
            "data_time": self.text_doc.data_time,
            "create_time": now_tz_datestring(),
        }
        await es.index(
            index=index,
            document=es_doc,
            id=self.doc_id,
            error_trace=True,
            refresh=True
        )