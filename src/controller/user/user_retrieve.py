#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import uuid
from enum import StrEnum

from pydantic import BaseModel, Field, model_validator
from sqlalchemy import select, update

from engine.rdb import g, fetch_one
from model.user_config import UserRetrieveConfigModel


class RetrieveField(StrEnum):
    filename = "filename"
    source = "source"
    author = "author"
    plain_text = "plain_text"


class UserRetrieveConfig(BaseModel):
    uid: str = Field(title="随机唯一标识", default=None)
    field: RetrieveField = Field(title="字段名称")
    value: str = Field(title="字段值")
    weight: float = Field(title="权重", ge=0.0, le=10.0, default=1.0)
    enable: bool = Field(title="是否启用", default=True)

    @model_validator(mode="after")
    def check_value(self):
        self.value = self.value.strip()
        if not self.value:
            raise ValueError(f"字段值不能为空")
        return self

    @model_validator(mode="after")
    def complete_uid(self):
        if self.uid is None:
            self.uid = uuid.uuid4().hex
        return self

    def to_function_score(self):
        """
        将用户检索配置转换为纯粹的 function_score 子条件
        当 match_phrase 到 value 时，权重倍数 * weight

        Returns:
            dict: function_score 的单个子条件，格式为 {"filter": {...}, "weight": weight}
        """
        if not self.enable:
            return []

        if self.field == RetrieveField.plain_text:
            fields = ["plain_text", "chunks.plain_content", "chunks.title"]
        else:
            fields = [self.field]

        return [
            {
                "filter": {
                    "match_phrase": {
                        field: self.value
                    }
                },
                "weight": self.weight
            } for field in fields]


class UserRetrieveController:
    @staticmethod
    async def get_one(user_id: int):
        query = (
            select(UserRetrieveConfigModel.config.label("user_retrieve_config"))
            .where(UserRetrieveConfigModel.create_user_id == user_id))
        config_data = await fetch_one(query)
        return config_data

    async def get_retrieve_config(self, user_id: int):
        user_retrieve_config = await self.get_one(user_id=user_id)
        if not user_retrieve_config:
            return []
        return [UserRetrieveConfig(**urc) for urc in user_retrieve_config["user_retrieve_config"]]

    async def get_function_score(self, user_id: int):
        user_retrieve_config = await self.get_retrieve_config(user_id=user_id)
        function_score = []
        for urc in user_retrieve_config:
            function_score.extend(urc.to_function_score())
        return function_score

    @staticmethod
    async def create(user_id: int, config: list[UserRetrieveConfig]):
        user_retrieve = UserRetrieveConfigModel(config=[urc.model_dump() for urc in config], create_user_id=user_id)
        g.session.add(user_retrieve)
        await g.session.flush()

        return user_retrieve.id

    async def update(self, user_id: int, config: list[UserRetrieveConfig]):
        exist_config = await self.get_one(user_id=user_id)
        if not exist_config:
            await self.create(user_id=user_id, config=config)
        else:
            query = (
                update(UserRetrieveConfigModel)
                .where(UserRetrieveConfigModel.create_user_id == user_id)
                .values({UserRetrieveConfigModel.config: [urc.model_dump() for urc in config]}))
            await g.session.execute(query)


UserRetrieve = UserRetrieveController()
