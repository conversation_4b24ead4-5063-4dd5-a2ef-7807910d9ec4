from exception.base import ApiError
from exception.error_code import *


class NotFoundError(ApiError):
    default_code = DataNotFoundCode


class ParamsCheckError(ApiError):
    default_code = ParamCheckErrorCode


class InsideServerError(ApiError):
    default_code = InsideServerErrorCode


class DataBaseError(ApiError):
    default_code = DataBaseErrorCode


class UserError(ApiError):
    default_code = UserErrorCode


class PasswordError(ApiError):
    default_code = PasswordErrorCode


class ExpireTokenError(ApiError):
    """该错误的默认错误码将返回401弹回到登录页"""
    default_code = ExpireTokenCode


class PermissionDenyError(ApiError):
    default_code = PermissionDenyCode


class UserLockError(ApiError):
    default_code = UserLockErrorCode


class SystemLockError(ApiError):
    default_code = SystemLockErrorCode


class ServiceConnectionError(ApiError):
    default_code = ExternalServerErrorCode


class AccessDeniedError(ApiError):
    default_code = AccessDeniedCode


class AuthDenyError(ApiError):
    default_code = AuthDenyCode


class AlreadyExistsError(ApiError):
    default_code = DataExistsErrorCode


class JsonParseError(ApiError):
    default_code = ParamCheckErrorCode


class UploadFileSizeError(ApiError):
    default_code = ParamCheckErrorCode


class FileTypeNotSupportError(ApiError):
    default_code = ParamCheckErrorCode


class ServiceUnavailableError(ApiError):
    default_code = ServiceUnavailableCode


class TenantTokensNotEnoughError(ApiError):
    default_code = TenantTokensNotEnoughCode
