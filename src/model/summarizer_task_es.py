#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from pydantic import BaseModel


SUMMARIZER_TASK_INDEX = "summarizer_task"

SUMMARIZER_TASK_MAPPING = {
    "settings": {
        "number_of_shards": 1,
        "number_of_replicas": 0,
        "index.mapping.nested_objects.limit": 10000,
        "analysis": {
            "filter": {
                # 停用词过滤器
                "stop_words_filter": {
                    "type": "stop",
                    "ignore_case": True,
                    "stopwords_path": "/usr/share/elasticsearch/config/stopwords.txt"
                },
                # 文本数字变化切分
                # 主要用来处理连续数字
                "word_delimiter_graph_filter": {
                    "type": "word_delimiter_graph",
                    "adjust_offsets": False  # 禁止修改偏移量
                },
                # 字符小写转换过滤器
                "lowercase_filter": {
                    "type": "lowercase"
                },
            },
            "analyzer": {
                "ik_smart": {
                    "type": "custom",
                    "tokenizer": "ik_smart",
                    "filter": [
                        "word_delimiter_graph_filter",  # 数字转换器
                        "lowercase_filter",  # 大小写转换器
                        "stop_words_filter",  # 停用词分词器
                    ]
                },
                "ik_max_word": {
                    "type": "custom",
                    "tokenizer": "ik_max_word",
                    "filter": [
                        "lowercase_filter",
                        "word_delimiter_graph_filter",
                    ]
                },
            },
        }
    },
    "mappings": {
        "dynamic": False,
        "properties": {
            # 摘要任务ID
            "summarizer_task_id": {
                "type": "integer"
            },
            # 租户ID
            "tenant_id": {
                "type": "integer"
            },
            # 摘要任务ID
            "summarizer_id": {
                "type": "integer"
            },
            # 相关文档ID
            "related_doc_ids": {
                "type": "keyword"
            },
            # model
            "model": {
                "type": "keyword"
            },
            # 结果
            "markdown": {
                "type": "text",
                "analyzer": "ik_max_word",
                "search_analyzer": "ik_smart"
            },
            "input_tokens": {
                "type": "integer",
            },
            "output_tokens": {
                "type": "integer",
            },
            # 数据时间
            "data_time": {
                "type": "date",
                "format": "yyyy-MM-dd HH:mm:ss||strict_date_optional_time||epoch_millis"
            },
            # 创建时间
            "create_time": {
                "type": "date",
                "format": "yyyy-MM-dd HH:mm:ss||strict_date_optional_time||epoch_millis"
            }
        }
    }
}


class SummarizerTaskDoc(BaseModel):
    tenant_id: int
    summarizer_task_id: int
    summarizer_id: int
    related_doc_ids: list[str | int]
    model: str | int
    markdown: str | None
    data_time: str
    create_time: str
    input_tokens: int = None
    output_tokens: int = None
