#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from typing import Annotated

import pandas as pd
from fastapi import Query
from pydantic import Field

from view import BaseView, api_description
from controller.analytics import Dashboard, Metrics, MetricShowType
from common.time import now_datetime_str
from exception import NotFoundError


class DashboardOverviewView(BaseView):
    @api_description(summary="获取看板")
    async def get(self,
                  dashboard_id: Annotated[int, Query(), Field(title="分析面板ID")],
                  task_id: Annotated[int, Query(), Field(title="分析任务ID")] = None):
        await Dashboard.get_one(dashboard_id=dashboard_id)
        tasks = await Metrics.get_task_all(dashboard_id=dashboard_id, max_task_id=task_id, depth=2, order_by="id:desc")
        if task_id and ((not tasks) or (tasks[0]["task_id"] != task_id)):
            raise NotFoundError(message="错误的任务ID")

        if len(tasks) == 2:
            current_round_task_id = tasks[0]["task_id"]
            previous_round_task_id = tasks[1]["task_id"]
        elif len(tasks) == 1:
            current_round_task_id = tasks[0]["task_id"]
            previous_round_task_id = None
        else:
            current_round_task_id = None
            previous_round_task_id = None

        dashboard_items = await Dashboard.get_item_all(dashboard_id=dashboard_id)
        dashboard_metrics = await Dashboard.get_metric_all(dashboard_id=dashboard_id)
        # [1. 初始化表]
        # 默认结构列
        columns = [
            {"name": "ID", "describe": "数据项ID", "type_": MetricShowType.default, "hidden": True},
            {"name": "名称", "describe": "数据项名称", "type_": MetricShowType.default, "hidden": False},
            {"name": "今日更新", "describe": "今日数据是否更新", "type_": MetricShowType.default, "hidden": False},
            {"name": "组合摘要任务", "describe": "组合摘要任务应用数量", "type_": MetricShowType.default, "hidden": False},
            {"name": "补充摘要任务", "describe": "补充摘要任务应用数量", "type_": MetricShowType.default, "hidden": False},
        ]

        # 业务指标列
        metric_id_name_mapping = {}  # {指标ID}+{任务ID}: 最终表格列头名
        for metric in dashboard_metrics:
            current_round_metric_name = metric["name"]
            previous_round_metric_name = metric["name"] + "(上轮)"
            columns.append({
                "name": current_round_metric_name,
                "describe": metric["require_"],
                "type_": MetricShowType.metric, "hidden": False,
                "task_id": current_round_task_id,
                "metric_id": metric["metric_id"],
                "metric_type": metric["type_"],
                "drill_down": True if current_round_task_id else False})
            columns.append({
                "name": previous_round_metric_name,
                "describe": "同名指标的上轮结果",
                "type_": MetricShowType.metric,
                "hidden": False,
                "task_id": previous_round_task_id,
                "metric_id": metric["metric_id"],
                "metric_type": metric["type_"],
                "drill_down": False})
            metric_id_name_mapping[f"{metric['metric_id']}_{current_round_task_id}"] = current_round_metric_name
            metric_id_name_mapping[f"{metric['metric_id']}_{previous_round_task_id}"] = previous_round_metric_name

        tables = []
        for item in dashboard_items:
            tables.append(
                {
                    "ID": item["item_id"],
                    "名称": item["name"],
                    "今日更新": "否",
                    "组合摘要任务": len(item["combin_summarizer_ids"]),
                    "补充摘要任务": len(item["sup_summarizer_ids"]),
                    **{col_name: None for col_name in metric_id_name_mapping.values()},
                }
            )
        df = pd.DataFrame(tables, index=[row["ID"] for row in tables])

        # [2. 指标数据插入]
        cell_extra = []
        now_str = now_datetime_str()
        metrics_data = await Metrics.get_metrics_data(
            dashboard_id=dashboard_id,
            task_ids=[task_id for task_id in [current_round_task_id, previous_round_task_id] if task_id is not None],
            item_ids=[it["item_id"] for it in dashboard_items])
        for md in metrics_data:
            if not md.get("metrics"):
                continue
            for task_metrics in md["metrics"]:
                # 与当日重合,重合时设定固定字段"今日更新"为是
                if task_metrics["data_time"][:10] == now_str[:10]:
                    df.loc[md["item_id"], ["今日更新"]] = "是"  # 今日更新列修改为是
                if not task_metrics["extract_metrics"]:
                    continue
                for m in task_metrics["extract_metrics"]:
                    mt_id = f"{m['metric_id']}_{task_metrics['task_id']}"
                    if mt_id in metric_id_name_mapping:
                        df.loc[md["item_id"], [metric_id_name_mapping[mt_id]]] = m["value"]
                        # 扩展单元格信息
                        if m.get("explanation"):
                            cell_extra.append({
                                "task_id": task_metrics["task_id"],
                                "metric_id": m["metric_id"],
                                "item_id": md["item_id"],
                                "extra": {
                                    "explanation": m["explanation"]
                                }
                            })

        return self.response(data={
            "columns": columns,
            "tables": df.values.tolist(),
            "current_round_task_id": current_round_task_id,
            "cell_extra": cell_extra
        })
