#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from typing import Annotated

from pydantic import Field
from fastapi import Body

from engine.rdb import g
from view import BaseView, api_description
from controller.user import UserRetrieve, UserRetrieveConfig


class UserRetrieveConfigView(BaseView):
    @api_description(summary="获取用户检索配置")
    async def get(self):
        config = await UserRetrieve.get_one(user_id=self.user_id)
        return self.response(data=config)


    @api_description(summary="更新用户检索配置")
    async def put(self,
                  user_retrieve_config: Annotated[list[UserRetrieveConfig], Body(embed=True), Field(title="模型配置")]):
        await UserRetrieve.update(user_id=self.user_id, config=user_retrieve_config)
        await g.session.commit()

        return self.response(message="更新成功")
