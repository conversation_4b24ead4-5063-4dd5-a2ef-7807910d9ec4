#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from typing import Annotated

from fastapi import Query, Body

from engine.rdb import g
from view import BaseView, api_description
from exception import NotFoundError, PermissionDenyError
from controller.repository import Repo, RepositoryType


class RepositoryListView(BaseView):
    @api_description(summary="查询知识库列表")
    async def get(self,
                  repo_id: Annotated[int, Query(title="知识库ID")] = None,
                  type_: Annotated[RepositoryType, Query(title="知识库类型")] = None,
                  match: Annotated[str, Query(title="模糊匹配")] = None,
                  page: Annotated[int, Query(title="分页页数")] = 1,
                  per_page: Annotated[int, Query(title="分页容量")] = 20,
                  order_by: Annotated[str, Query(title="排序字段")] = "create_time:desc"):
        user_id = None if self.is_tenant_admin else self.user_id
        pager, repositories = await Repo.get_list(
            repo_id=repo_id, type_=type_, match=match, user_id=user_id, page=page, per_page=per_page,
            order_by=order_by)

        return self.response(data=repositories, pager=pager)


class RepositoryView(BaseView):
    @api_description(summary="创建知识库")
    async def post(self,
                   name: Annotated[str, Body(title="知识库名称", max_length=24)],
                   extract_model_id: Annotated[int, Body(title="知识库配置")],
                   type_: Annotated[RepositoryType, Body(title="知识库类型")] = RepositoryType.private,):
        repo_id = await Repo.create(name=name, type_=type_, extract_model_id=extract_model_id)
        await g.session.commit()

        return self.response(data={"repo_id": repo_id})

    @api_description(summary="修改知识库")
    async def put(self,
                  repo_id: Annotated[int, Body(title="知识库ID")],
                  name: Annotated[str, Body(title="知识库名称")] = None,
                  type_: Annotated[RepositoryType, Body(title="知识库类型")] = None,
                  extract_model_id: Annotated[int, Body(title="知识库配置")] = None):
        user_id = None if self.is_tenant_admin else self.user_id
        if not (repo := await Repo.get_one(repo_id=repo_id, user_id=user_id)):
            raise NotFoundError("无法修改无权限知识库")
        if not repo["edit"]:
            raise PermissionDenyError("非创建人或超管无法修改该知识库")

        await Repo.update(repo_id=repo_id, name=name, type_=type_, extract_model_id=extract_model_id)
        await g.session.commit()

        return self.response(message="修改成功")

    @api_description(summary="删除知识库")
    async def delete(self,
                     repo_id: Annotated[int, Body(title="知识库ID", embed=True)]):
        user_id = None if self.is_tenant_admin else self.user_id
        if not (repo := await Repo.get_one(repo_id=repo_id, user_id=user_id)):
            raise NotFoundError("无法删除无权限知识库")
        if not repo["edit"]:
            raise PermissionDenyError("非创建人或超管无法删除该知识库")

        await Repo.delete(repo_id=repo_id)
        await g.session.commit()

        return self.response(message="删除成功")
