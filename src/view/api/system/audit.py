#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from typing import Annotated

from pydantic import Field
from fastapi import Query

from view import BaseView, api_description
from controller.stats import TokenCounts, LLMBusiness
from exception import PermissionDenyError


class AuditStatsView(BaseView):
    @api_description(summary="获取审计日志列表")
    async def get(self,
                  start: Annotated[str, Query(), Field(title="起始时间")] = None,
                  end: Annotated[str, Query(), Field(title="结束时间")] = None):
        if not self.is_tenant_admin:
            raise PermissionDenyError()

        user_stats = await TokenCounts.get_user_stats(start=start, end=end)
        business_stats = await TokenCounts.get_business_stats(start=start, end=end)
        return self.response(data={
            "user_stats": user_stats,
            "business_stats": business_stats
        })


class AuditLogView(BaseView):
    @api_description(summary="获取审计日志列表")
    async def get(self,
                  start: Annotated[str, Query(), Field(title="起始时间")] = None,
                  end: Annotated[str, Query(), Field(title="结束时间")] = None,
                  business: Annotated[LLMBusiness, Query(), Field(title="业务内容")] = None,
                  match: Annotated[str, Query(), Field(title="模糊匹配")] = None,
                  page: Annotated[int, Query(), Field(title="页码")] = 1,
                  per_page: Annotated[int, Query(), Field(title="每页数量")] = 20,
                  order_by: Annotated[str, Query(), Field(title="排序字段")] = "create_time:desc"):
        if not self.is_tenant_admin:
            raise PermissionDenyError()

        pager, logs = await TokenCounts.get_list(
            start=start, end=end, match=match, page=page, per_page=per_page, order_by=order_by)
        return self.response(data=logs, pager=pager)
