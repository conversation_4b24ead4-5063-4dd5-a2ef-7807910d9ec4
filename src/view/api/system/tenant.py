#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from typing import Annotated

from fastapi import Query, Body
from pydantic import Field

from view import BaseView, api_description
from engine.rdb import g
from common.sm import gm_sm2, gm_sm4
from controller.tenant import  User, Auth, Strategy, StrategyConfig
from controller.system import Tenant
from exception import PermissionDenyError, AlreadyExistsError, ParamsCheckError, NotFoundError


class TenantListView(BaseView):
    @api_description(summary="获取租户列表")
    async def get(self,
                  match: Annotated[str, Query(), Field(title="匹配字符")] = None,
                  page: Annotated[int, Query(), Field(title="页码")] = 1,
                  per_page: Annotated[int, Query(), Field(title="每页数量")] = 20,
                  order_by: Annotated[str, Query(), Field(title="排序字段")] = "create_time:desc"):
        if not self.is_super_admin:
            raise PermissionDenyError()

        pager, tenants = await Tenant.get_list(match=match, page=page, per_page=per_page, order_by=order_by)

        return self.response(data=tenants, pager=pager)


class TenantView(BaseView):
    @api_description("创建租户")
    async def post(self,
                   name: Annotated[str, Body(), Field(title="租户名称")],
                   plan_id: Annotated[int, Body(), Field(title="Token限制")],
                   tenant_admin_nickname: Annotated[str, Body(), Field(title="租户管理员昵称")],
                   tenant_admin_username: Annotated[str, Body(), Field(title="租户管理员登录账号")],
                   tenant_admin_password: Annotated[str, Body(), Field(title="租户管理员密码")]):
        if not self.is_super_admin:
            raise PermissionDenyError()

        if await User.check_username(username=tenant_admin_username):
            raise AlreadyExistsError(message="登录账号已存在,请更换登录账号")
        try:
            password_plaintext = gm_sm2.decrypt(tenant_admin_password)
            password = gm_sm4.encrypt(password_plaintext)
        except Exception:
            raise ParamsCheckError(f"密码不合法")

        tenant_id = await Tenant.create(name=name, plan_id=plan_id)
        await User.create(
            tenant_id=tenant_id, username=tenant_admin_username, password=password, role_ids=[-2],  # -2: 租户管理员
            nickname=tenant_admin_nickname)
        await Strategy.create(strategy=StrategyConfig(), tenant_id=tenant_id)
        await g.session.commit()

        return self.response(data={"tenant_id": tenant_id}, message="创建成功")

    @api_description("修改租户")
    async def put(self,
                  tenant_id: Annotated[int, Body(), Field(title="租户ID")],
                  name: Annotated[str, Body(), Field(title="租户名称")] = None,
                  plan_id: Annotated[int, Body(), Field(title="套餐ID")] = None):
        if not self.is_super_admin:
            raise PermissionDenyError()

        if plan_id is not None:
            await Tenant.create_plan(tenant_id=tenant_id, plan_id=plan_id)
        await Tenant.update(tenant_id=tenant_id, name=name, plan_id=plan_id)
        await g.session.commit()

        return self.response(message="修改成功")

    @api_description("删除租户")
    async def delete(self,
                     tenant_id: Annotated[int, Body(embed=True), Field(title="租户ID")]):
        if not self.is_super_admin:
            raise PermissionDenyError()

        await Tenant.delete(tenant_id=tenant_id)
        await g.session.commit()

        return self.response(message="删除成功")


class TenantPlanSubscribeListView(BaseView):
    @api_description("获取租户套餐订阅列表")
    async def get(self,
                  tenant_id: Annotated[int, Query(), Field(title="租户ID")],
                  page: Annotated[int, Query(), Field(title="页码")] = 1,
                  per_page: Annotated[int, Query(), Field(title="每页数量")] = 20,
                  order_by: Annotated[str, Query(), Field(title="排序字段")] = "create_time:desc"):
        if not self.is_super_admin:
            raise PermissionDenyError()

        pager, plans = await Tenant.get_plan_list(
            tenant_id=tenant_id, is_delete=None, page=page, per_page=per_page, order_by=order_by)

        return self.response(data=plans, pager=pager)


class TenantSwitchView(BaseView):
    @api_description("超级管理员切换租户")
    async def put(self,
                  tenant_id: Annotated[int, Body(embed=True), Field(title="租户ID")]):
        if not self.is_super_admin:
            raise PermissionDenyError()
        tenant = await Tenant.get_one(tenant_id=tenant_id)
        if not tenant:
            raise NotFoundError(message="未找到目标租户")

        await User.update(user_id=self.user_id, tenant_id=tenant_id)
        await g.session.commit()

        current_user = await User.get_user(user_id=self.user_id, tenant_id=tenant_id)
        access_token, exp = Auth.create_token(user_id=self.user_id)
        await Auth.set_token_cache(user_id=current_user["user_id"], exp=exp, data=current_user)

        current_user["access_token"] = access_token

        return self.response(data=current_user, message="切换成功")
