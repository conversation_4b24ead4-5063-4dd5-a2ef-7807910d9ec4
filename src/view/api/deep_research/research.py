from typing import Annotated

from fastapi import Body

from common import g
from controller.chat.deep_research import DeepResearch, DeepResearchStatus
from tasks import research_plan_make, research_execute
from controller.chat.session import Session, ChatSessionType
from exception import NotFoundError
from view import BaseView, api_description


class ResearchRecoverView(BaseView):
    @api_description(summary="恢复深入研究计划")
    async def post(self, session_id: Annotated[int, Body(title="会话ID", embed=True)]):
        stream = DeepResearch.get_stream(session_id=session_id)
        return self.stream(stream)


class ResearchPlanView(BaseView):
    @api_description(summary="开始深入研究计划")
    async def post(self,
                   user: Annotated[str, Body(title="用户输入的问题")],
                   session_id: Annotated[int, Body(title="会话ID")]):
        session = await Session.get_one(session_id=session_id, session_type=ChatSessionType.DEEP_RESEARCH)
        if session is None:
            raise NotFoundError("未找到指定的会话")

        message = await DeepResearch.get_message(session_id=session_id, status=DeepResearchStatus.pending)
        if message is None:
            raise NotFoundError("未找到合适的深入研究计划模版")

        await DeepResearch.update_message(session_id=session_id, user=user, status=DeepResearchStatus.planning)
        await g.session.commit()

        await research_plan_make.kiq(session_id=session_id, user=user)
        await Session.start_streaming(session_id=session_id)
        stream = DeepResearch.get_stream(session_id=session_id)
        return self.stream(stream)


class ResearchView(BaseView):
    @api_description(summary="开始深入研究")
    async def post(self,
                   user: Annotated[str, Body(title="用户输入的问题")],
                   plan: Annotated[str, Body(title="修改后的研究计划")],
                   session_id: Annotated[int, Body(title="会话ID")]):
        session = await Session.get_one(session_id=session_id, session_type=ChatSessionType.DEEP_RESEARCH)
        if session is None:
            raise NotFoundError("未找到指定的会话")

        message = await DeepResearch.get_message(session_id=session_id, status=DeepResearchStatus.planed)
        if message is None:
            raise NotFoundError("未找到合适的深入研究计划模版")

        await DeepResearch.update_message(session_id=session_id, user_plan=plan, status=DeepResearchStatus.researching)
        await g.session.commit()

        await research_execute.kiq(session_id=session_id, user=user, plan=plan)
        await Session.start_streaming(session_id=session_id)
        stream = DeepResearch.get_stream(session_id=session_id)
        return self.stream(stream)
