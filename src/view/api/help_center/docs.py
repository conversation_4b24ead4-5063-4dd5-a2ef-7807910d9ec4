#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import re
from typing import Annotated

import mistune
from fastapi import Path
from fastapi.responses import HTMLResponse

from view import BaseView, api_description


def custom_slugify(content: str, separator: str = "-"):
    """
    自定义slugify函数，正确处理中文字符
    """
    # 移除markdown语法
    content = re.sub(r'^#+\s*', '', content)

    # 保留中文、英文、数字，移除特殊字符（除了连字符和空格）
    content = re.sub(r'[^\w\u4e00-\u9fff\s-]', '', content)

    # 移除多余空格，但保留连字符
    content = re.sub(r'\s+', '', content)

    return content


class CustomHTMLRenderer(mistune.HTMLRenderer):
    """自定义HTML渲染器，支持标题锚点"""

    def heading(self, text, level, **attrs):
        """重写heading方法，生成带锚点的标题"""
        anchor = custom_slugify(text)
        return f'<h{level} id="{anchor}">{text}</h{level}>\n'


class ModuleDocumentView(BaseView):
    @api_description(
        summary="帮助中心",
        authentication_classes=[],
        response_class=HTMLResponse
    )
    async def get(self,
                  module: Annotated[str, Path(title="模块")]):
        # 读取 Markdown 文件
        with open(f"../docs/help_center/{module}.md", mode="r", encoding="utf-8") as f:
            md_content = f.read()

        # 使用mistune 3.x转换，支持更好的嵌套列表、表格、引用等格式
        renderer = CustomHTMLRenderer()

        # 启用常用插件以支持更多markdown格式
        plugins = [
            'strikethrough',  # 删除线 ~~text~~
            'table',          # 表格支持
            'footnotes',      # 脚注支持
            'url',            # 自动链接
            'task_lists',     # 任务列表 - [x] 完成
            'def_list',       # 定义列表
            'mark',           # 标记 ==text==
            'insert',         # 插入 ++text++
            'superscript',    # 上标 x^2^
            'subscript',      # 下标 H~2~O
            'abbr',           # 缩写
        ]

        markdown_parser = mistune.create_markdown(renderer=renderer, plugins=plugins)
        html_content = markdown_parser(md_content)

        # 返回完整的 HTML 页面
        return f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <style>
                body {{
                    display: flex;
                    justify-content: center;
                    align-items: flex-start;
                    min-height: 100vh;
                    margin: 0;
                    background-color: #f9f9f9;
                    padding: 20px 0;
                }}
                .markdown-body {{
                    box-sizing: border-box;
                    max-width: 980px;
                    width: 100%;
                    padding: 45px;
                    background-color: white;
                    border-radius: 8px;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
                }}

                /* 改进的列表样式 */
                ul {{
                    margin: 10px 0;
                    padding-left: 20px;
                }}
                ul ul {{
                    margin: 5px 0;
                    padding-left: 25px;
                }}
                li {{
                    margin: 8px 0;
                    line-height: 1.6;
                }}

                /* 表格样式 */
                table {{
                    border-collapse: collapse;
                    width: 100%;
                    margin: 20px 0;
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                }}
                th, td {{
                    border: 1px solid #ddd;
                    padding: 12px 8px;
                    text-align: left;
                }}
                th {{
                    background-color: #f8f9fa;
                    font-weight: 600;
                    color: #495057;
                }}
                tr:nth-child(even) {{
                    background-color: #f8f9fa;
                }}
                tr:hover {{
                    background-color: #e9ecef;
                }}

                /* 引用样式 */
                blockquote {{
                    border-left: 4px solid #007bff;
                    margin: 20px 0;
                    padding: 15px 20px;
                    background-color: #f8f9fa;
                    border-radius: 0 4px 4px 0;
                    color: #495057;
                }}
                blockquote p {{
                    margin: 0 0 10px 0;
                }}
                blockquote p:last-child {{
                    margin-bottom: 0;
                }}
                blockquote blockquote {{
                    border-left: 4px solid #6c757d;
                    background-color: #e9ecef;
                    margin: 10px 0;
                }}

                /* 代码样式 */
                pre {{
                    background-color: #f8f9fa;
                    border: 1px solid #e9ecef;
                    border-radius: 4px;
                    padding: 15px;
                    overflow-x: auto;
                    margin: 15px 0;
                }}
                code {{
                    background-color: #f8f9fa;
                    padding: 2px 6px;
                    border-radius: 3px;
                    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
                    font-size: 0.9em;
                }}
                pre code {{
                    background-color: transparent;
                    padding: 0;
                    border-radius: 0;
                }}

                /* 水平分割线 */
                hr {{
                    border: none;
                    border-top: 2px solid #e9ecef;
                    margin: 30px 0;
                }}

                /* 任务列表样式 */
                .task-list-item {{
                    list-style: none;
                }}
                .task-list-item input[type="checkbox"] {{
                    margin-right: 8px;
                }}

                /* 删除线、标记等样式 */
                del {{
                    color: #6c757d;
                }}
                mark {{
                    background-color: #fff3cd;
                    padding: 2px 4px;
                    border-radius: 2px;
                }}
                ins {{
                    background-color: #d4edda;
                    text-decoration: none;
                    padding: 2px 4px;
                    border-radius: 2px;
                }}
                sup, sub {{
                    font-size: 0.8em;
                }}

                /* 标题锚点样式 */
                h1, h2, h3, h4, h5, h6 {{
                    position: relative;
                }}

                @media (max-width: 767px) {{
                    .markdown-body {{
                        padding: 15px;
                    }}
                }}
            </style>
        </head>
        <body>
            <div class="markdown-body">
                {html_content}
            </div>
        </body>
        </html>
        """