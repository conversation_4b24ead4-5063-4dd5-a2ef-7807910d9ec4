import inspect
from datetime import datetime, date
from decimal import Decimal
from typing import Union, AsyncIterable, Iterable

from fastapi import APIRouter, Depends
from fastapi.responses import JSONResponse
from starlette.responses import StreamingResponse

from common import g
from common.schema import Pager
from engine.rdb import get_db, get_db_sync
from controller.tenant import BaseTokenAuthentication, RoleType

base_router = APIRouter()


def custom_serializer(obj):
    if isinstance(obj, (datetime, date)):
        return obj.strftime("%Y-%m-%d %H:%M:%S")
    if isinstance(obj, Decimal):
        return float(obj)
    raise TypeError(f"Type {type(obj)} not serializable")


# 自定义 JSONResponse 类
class CustomJSONResponse(JSONResponse):
    def render(self, content) -> bytes:
        import json
        return json.dumps(
            content,
            default=custom_serializer,  # 使用自定义序列化逻辑
            ensure_ascii=False,
            allow_nan=False,
            indent=None,
            separators=(",", ":"),
        ).encode("utf-8")


class BaseView:
    authentication_classes = [BaseTokenAuthentication]
    permissions_classes = []
    path: str = None

    def __init__(self, path: str, tags: list[str]):
        self.path = path or getattr(self, "path")
        self.tags = tags or getattr(self, "tags", None)
        self.register_routes()

    @property
    def request(self):
        return g.request

    @property
    def user(self):
        return g.user

    @property
    def user_id(self):
        return g.user.id

    @property
    def role_ids(self):
        return g.user.role_ids

    @property
    def is_tenant_admin(self):
        return any([r for r in g.user.roles if r["role_type"] in (RoleType.tenant_admin, RoleType.super_admin)])

    @property
    def is_super_admin(self):
        return any([r for r in g.user.roles if r["role_type"] == RoleType.super_admin])

    @staticmethod
    def response(code: int = 0, message: str = "请求成功", data: Union[dict, None, list, str] = None,
                 pager: Pager = None):
        if isinstance(data, list):
            if pager:
                data = {"items": data or [], **pager.model_dump()}
            else:
                data = {"items": data or []}

        return CustomJSONResponse(content={
            "code": code,
            "message": message,
            "trace_id": g.trace_id,
            "data": data or {}
        })

    @staticmethod
    def stream(generator: AsyncIterable[str | bytes] | Iterable[str | bytes]):
        return StreamingResponse(
            generator,
            media_type="text/event-stream",
        )

    def get_dependencies(self, extra_params: dict, method):
        if inspect.iscoroutinefunction(method):
            dependencies = [Depends(get_db)]
        else:
            dependencies = [Depends(get_db_sync)]

        custom_authentication_classes = extra_params.pop("authentication_classes")\
            if extra_params.get("authentication_classes") is not None else self.authentication_classes
        for auth_class in custom_authentication_classes:
            dependencies.append(Depends(auth_class()(method)))

        return dependencies

    def register_routes(self):
        method_map = {
            "get": {'path': self.path, 'methods': ['GET'], },
            'post': {'path': self.path, 'methods': ['POST'], },
            'put': {'path': self.path, 'methods': ['PUT'], },
            'delete': {'path': self.path, 'methods': ['DELETE'], },
        }
        for method_name, router_info in method_map.items():
            method = getattr(self, method_name, None)
            if self.is_method_overridden(method_name):
                extra_params = getattr(method, '_extra_params', {})
                dependencies = self.get_dependencies(extra_params, method)
                base_router.add_api_route(router_info['path'], method,
                                          methods=router_info['methods'],
                                          dependencies=dependencies,
                                          tags=self.tags, **extra_params)

    def is_method_overridden(self, method_name: str) -> bool:
        subclass_method = getattr(self, method_name, None)
        base_method = getattr(BaseView, method_name, None)
        if not subclass_method or not base_method:
            return False
        # Check if the method is overridden
        return inspect.getmodule(subclass_method) != inspect.getmodule(base_method)

    def get(self, *args, **kwargs):
        raise ImportError("Not implemented")

    def post(self, *args, **kwargs):
        raise ImportError("Not implemented")

    def put(self, *args, **kwargs):
        raise ImportError("Not implemented")

    def delete(self, *args, **kwargs):
        raise ImportError("Not implemented")