#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import datetime
import hashlib
import json

import pandas as pd
import akshare as ak
import baostock as bs
import pandas_ta as ta  # 不可删除

from engine.es import es
from engine.taskiq_task import broker
from model.doc import ALL_REPO_INDEX
from common.logger import logger
from common.time import strftime, strptime
from exception import AlreadyExistsError
from controller.repository import DefaultRepoDataRange, TextDoc, Doc, DocStatus
from controller.parser import TextDocParser
from controller.parser.chunker import HtmlChunker, BGE_M3_TOKENIZER
from controller.engine import EmbeddingEngine, EmbeddingModel


"""
[?] 概念板块(时间/概念板块/相关股票/开盘/收盘/最高/最低/涨跌幅/涨跌额/成交量/成交额/振幅/换手率)
[x] 技术面(时间/股票代码/开盘/收盘/最高/最低/成交额/涨跌幅/换手率)
[x] 个股价值/资金流向(股票代码/总市值/流通市值/行业/PE/市净率/市现率/市销率/PEG/流入资金/流出资金)
[x] 机构持股(时间/股票代码/机构持股)
[x]  公司动态(时间/股票代码/事项)
[x]  分析师评级(时间/股票代码/分析师/当前评级)
[x]  千股千评(时间/股票代码/最新价/机构参与度/主力成本/市盈率/综合得分/上升)
[x]  个股新闻(时间/股票代码/标题/内容/链接/来源)
[x]  财经内容精选(时间/链接/摘要)
[x]  东财快讯(时间/股票代码/摘要)
[x]  业绩报表(时间/股票代码/营业收入/同比增长/季度环比增长/净利润/同比增长/季度环比增长/净资产收益率/销售毛利率)
[x]  业绩预期(时间/股票代码/预告类型/预测指标/业绩变动/业绩变动原因)
[]  筹码分布(时间/股票代码/获利比例/平均成本/90集中度/70集中度)
[x]  个股研报(时间/股票代码/东财评级/机构/盈利预测-收益/盈利预测-市盈率) 
[]  个股上榜统计(股票代码/最近上榜日/龙虎榜净买额/买方机构次数/卖方机构次数/机构买入净额)
[]  股票热度(时间/股票代码/雪球热度排名/雪球热度变化/问财热度排名/问财热度变化/东财热度排名/东财热度变化/人气榜排名/热搜关键词)
"""

DOC_EXTRACT_ENABLED = True


class AkshareDataOfflineTask:
    def __init__(self):
        self.data_range = [
            {"code": "300750", "code_name": "宁德时代", "market": "sz", "repo_id": -21},
            {"code": "601012", "code_name": "隆基绿能", "market": "sh", "repo_id": -22},
        ]
        self.code_mapping = {stock["code"]: stock for stock in self.data_range}


    async def get_stock_news(self):
        """
        个股新闻
        https://akshare.akfamily.xyz/data/stock/stock.html#id126"""
        func_tag = "个股新闻"
        for stock in self.data_range:
            code = stock["code"]
            tags = [code, func_tag]
            last_data_time = await self.get_last_data_time(repo_id=stock["repo_id"], tags=tags)

            stock_news_em_df = ak.stock_news_em(symbol=stock["code"])
            stock_news_em_df = stock_news_em_df[stock_news_em_df["发布时间"] > last_data_time]

            for i, row in stock_news_em_df.iterrows():
                md5 = self._md5(row["新闻内容"])
                title = row["新闻标题"]
                text_doc = TextDoc(
                    md5=md5,
                    data_time=row["发布时间"],
                    repo_id=stock["repo_id"],
                    title=title,
                    source=row["文章来源"],
                    url=row["新闻链接"],
                    author="东方财富-个股新闻",
                    content=row["新闻内容"],
                    tags=tags,
                )
                await self.parsing(text_doc=text_doc)

    async def get_stock_main_news_cx(self):
        """
        财经内容精选
        https://akshare.akfamily.xyz/data/stock/stock.html#id126"""
        func_tag = "财经内容精选"
        repo_id = -22  # 临时占位
        last_data_time = await self.get_last_data_time(repo_id=repo_id)

        stock_news_main_cx_df = ak.stock_news_main_cx()
        stock_news_main_cx_df["pub_time"] = stock_news_main_cx_df["pub_time"].apply(lambda x: x[:19])
        stock_news_main_cx_df = stock_news_main_cx_df[stock_news_main_cx_df["pub_time"] > last_data_time]

        for i, row in stock_news_main_cx_df.iterrows():
            md5 = self._md5(row["url"])
            text_doc = TextDoc(
                md5=md5,
                data_time=row["pub_time"],
                repo_id=repo_id,
                title=row["tag"],
                author="财新网-财经内容精选",
                url=row["url"],
                content=row["summary"],
                tags=["func_tag"],
            )
            await self.parsing(text_doc=text_doc)

    async def get_stock_info_global_ths(self):
        """
        全球财经直播-同花顺财经
        https://akshare.akfamily.xyz/data/stock/stock.html#id399"""
        func_tag = "全球财经直播"
        repo_id = -24  # 临时占位
        last_data_time = await self.get_last_data_time(repo_id=repo_id)

        stock_info_global_ths_df = ak.stock_info_global_ths()
        stock_info_global_ths_df = stock_info_global_ths_df[stock_info_global_ths_df["发布时间"] > last_data_time]

        for i, row in stock_info_global_ths_df.iterrows():
            md5 = self._md5(row["链接"])
            title = row["标题"]
            text_doc = TextDoc(
                md5=md5,
                data_time=row["发布时间"],
                repo_id=repo_id,
                title=title,
                author="同花顺财经-全球财经直播",
                url=row["链接"],
                content=row["内容"],
                tags=func_tag,
            )
            await self.parsing(text_doc=text_doc)


    async def get_stock_zh_a_hist(self):
        """
        历史行情数据+技术面分析
        https://akshare.akfamily.xyz/data/stock/stock.html#id22"""
        func_tag = "技术指标"
        bs.login()
        for stock in self.data_range:
            code = stock["code"]
            market = stock["market"]
            code_name = stock["code_name"]
            tags = [code, func_tag]
            last_data_time = await self.get_last_data_time(repo_id=stock["repo_id"], tags=tags)

            rs = bs.query_history_k_data_plus(
                code=f"{market}.{code}",
                fields="date,open,high,low,close,volume,turn,peTTM,pctChg,pbMRQ,pbMRQ,psTTM,pcfNcfTTM",
                start_date=(strptime(last_data_time[:10]) - datetime.timedelta(days=60)).strftime("%Y-%m-%d"),
                end_date=datetime.date.today().strftime("%Y-%m-%d"),
                frequency="d", adjustflag="2")
            data_list = []
            while rs.next():
                # 获取一条记录，将记录合并在一起
                data_list.append(rs.get_row_data())
            history_daily_df = pd.DataFrame(data_list, columns=rs.fields)
            history_daily_df = history_daily_df.set_index(keys="date", drop=False)
            history_daily_df["close"] = history_daily_df["close"].astype(float)

            numeric_cols = ["open", "high", "low", "close", "volume", "turn"]
            history_daily_df[numeric_cols] = history_daily_df[numeric_cols].apply(pd.to_numeric, errors="coerce")

            history_daily_df.ta.rsi(length=14, append=True)
            history_daily_df.ta.atr(length=30, append=True)
            history_daily_df = history_daily_df.round(2)
            history_daily_df["date"] = history_daily_df["date"].apply(lambda x: x + " 15:00:00")
            history_daily_df = history_daily_df[history_daily_df["date"] > last_data_time]
            if len(history_daily_df) == 0:
                continue

            history_daily_df = history_daily_df.rename(columns={
                "open": "开盘价",
                "close": "收盘价",
                "high": "最高价",
                "low": "最低价",
                "volume": "成交量",
                "turn": "换手率",
                "peTTM": "滚动市盈率",
                "pctChg": "涨跌幅（百分比）",
                "psTTM": "滚动市销率",
                "pbMRQ": "市净率",
                "pcfNcfTTM": "滚动市现率",
                # 计算指标
                "ATRr_30": "30日波动率",
                "RSI_14": "14日RSI"
            })

            # logger.info(f"{doc_code} 新数据: {len(history_daily_df)}")
            for day, row in history_daily_df.iterrows():
                md5 = self._md5(f"{code}_{day.replace('-', '')}_kline_indicator")
                title = f"[{code_name}]{day} K线数据"
                text_doc = TextDoc(
                    md5=md5,
                    data_time=row["date"],
                    repo_id=stock["repo_id"],
                    title=title,
                    source="baostock",
                    author="baostock",
                    content=self._kv_text(row=row, exclude=["date"]),
                    tags=tags,
                )
                await self.parsing(text_doc=text_doc)

    async def get_stock_value_flow(self):
        """
        个股估值 + 资金流入流出
        https://akshare.akfamily.xyz/data/stock/stock.html#id281
        https://akshare.akfamily.xyz/data/stock/stock.html#id163
        """
        func_tag = "估值和资金流动"
        for stock in self.data_range:
            code = stock["code"]
            market = stock["market"]
            code_name = stock["code_name"]
            tags = [code, func_tag]
            last_data_time = await self.get_last_data_time(repo_id=stock["repo_id"], tags=tags)

            # 个股估值
            stock_value_em_df = ak.stock_value_em(symbol=code)
            stock_value_em_df = stock_value_em_df.tail(400)
            # 个股资金流
            stock_individual_fund_flow_df = ak.stock_individual_fund_flow(stock=code, market=market)
            stock_individual_fund_flow_df["数据日期"] = stock_individual_fund_flow_df["日期"]

            stock_value_flow = pd.merge(left=stock_value_em_df, right=stock_individual_fund_flow_df, on="数据日期", how="inner")
            stock_value_flow["数据日期"] = stock_value_flow["数据日期"].apply(lambda x: strftime(x, "%Y-%m-%d") + " 15:00:00")
            stock_value_flow = stock_value_flow[stock_value_flow["数据日期"] > last_data_time]
            stock_value_flow["总市值(万元)"] = stock_value_flow["总市值"] / 10000  # 转为万元
            stock_value_flow["流通市值(万元)"] = stock_value_flow["流通市值"] / 10000  # 转为万元
            stock_value_flow["总股本(万元)"] = stock_value_flow["总股本"] / 10000  # 转为万元
            stock_value_flow["流通股本(万元)"] = stock_value_flow["流通股本"] / 10000  # 转为万元
            stock_value_flow = stock_value_flow.round(2)  # 全部浮点数保留两位小数
            stock_value_flow = stock_value_flow.drop(columns=["当日收盘价", "当日涨跌幅", "PE(静)", "日期", "收盘价", "涨跌幅"], errors="ignore")
            # logger.info(f"{doc_code} 新数据: {len(stock_value_flow)}")

            for _, row in stock_value_flow.iterrows():
                md5 = self._md5(f"{code}_{row['数据日期'][:10].replace('-', '')}_value_flow")
                title = f"[{code_name}]{row['数据日期'][:10]}估值和资金流向"
                text_doc = TextDoc(
                    md5=md5,
                    data_time=row["数据日期"],
                    repo_id=stock["repo_id"],
                    title=title,
                    content=self._kv_text(row=row, exclude=["数据日期", "总市值", "流通市值", "总股本", "流通股本"]),
                    author="东方财富-个股估值和资金流向",
                    tags=tags,
                )
                await self.parsing(text_doc=text_doc)


    async def get_stock_institute_hold(self):
        """
        机构持股(包含基金)
        https://akshare.akfamily.xyz/data/stock/stock.html#id245
        """
        func_tag = "机构持股"
        for stock in self.data_range:
            code = stock["code"]
            code_name = stock["code_name"]
            tags = [code, func_tag]
            last_data_time = await self.get_last_data_time(repo_id=stock["repo_id"], tags=tags)

            # 基金持股估值
            for q in ("20244", "20251", "20252"):
                if q.endswith("1"):
                    title_q = f"{q[:4]}一季度"
                    data_time = f"{q[:4]}-03-31 00:00:00"
                elif q.endswith("2"):
                    title_q = f"{q[:4]}二季度"
                    data_time = f"{q[:4]}-06-30 00:00:00"
                elif q.endswith("3"):
                    title_q = f"{q[:4]}三季度"
                    data_time = f"{q[:4]}-09-30 00:00:00"
                else:
                    title_q = f"{q[:4]}四季度"
                    data_time = f"{q[:4]}-12-31 00:00:00"
                if last_data_time >= data_time:
                    continue

                # 机构持股
                stock_institute_hold_detail_df = ak.stock_institute_hold_detail(stock=code, quarter=q)
                stock_institute_hold_detail_df = stock_institute_hold_detail_df.head(20)  # TOP 20
                stock_institute_hold_detail_df = stock_institute_hold_detail_df.drop(
                    columns=["持股机构代码", "持股机构全称", "持股数", "最新持股数", "持股比例", "占流通股比例"], errors="ignore")
                stock_institute_hold_detail_df = stock_institute_hold_detail_df.round(2)  # 全部浮点数保留两位小数

                # logger.info(f"{doc_code} 新数据: {len(stock_institute_hold_detail_df)}")

                md5 = self._md5(f"{code}_{data_time[:10].replace('-', '')}_institute_hold")
                title = f"[{code_name}]{title_q}机构持股"
                text_doc = TextDoc(
                    md5=md5,
                    data_time=data_time,
                    repo_id=stock["repo_id"],
                    title=title,
                    author="新浪财经-机构持股",
                    content=stock_institute_hold_detail_df.to_string(),
                    html_content=stock_institute_hold_detail_df.to_html(index=False),
                    tags=tags,
                )
                await self.parsing(text_doc=text_doc)

    async def get_stock_company_news(self):
        """
        公司动态
        https://akshare.akfamily.xyz/data/stock/stock.html#id41
        """
        func_tag = "公司动态"
        last_data_time = await self.get_last_data_time(tags=[func_tag])
        last_date = strptime(last_data_time).date()
        today = datetime.date.today()
        date_list = [last_date + datetime.timedelta(days=i) for i in range((today - last_date).days + 1)]

        for d in date_list:
            date_str = d.strftime("%Y%m%d")
            try:
                stock_gsrl_gsdt_em_df = ak.stock_gsrl_gsdt_em(date=date_str)
            except Exception as err:
                continue

            for _, row in stock_gsrl_gsdt_em_df.iterrows():
                code = row["代码"]
                if code not in self.code_mapping:
                    continue
                tags = [code, func_tag]
                md5 = self._md5(row["具体事项"])
                stock = self.code_mapping[code]
                title = f"[{stock['code_name']}]{row['事件类型']}事件"
                text_doc = TextDoc(
                    md5=md5,
                    data_time=d.strftime("%Y-%m-%d") + " 00:00:00",
                    repo_id=stock["repo_id"],
                    title=title,
                    author="东方财富-公司动态",
                    content=row["具体事项"],
                    tags=tags
                )
                await self.parsing(text_doc=text_doc)

    async def get_stock_performance_report(self):
        """
        业绩快报
        https://akshare.akfamily.xyz/data/stock/stock.html#id132
        """
        func_tag = "业绩快报"
        for q in ("20241231", "20250331", "20250630"):
            stock_yjbb_em_df = ak.stock_yjbb_em(date=q)
            stock_yjbb_em_df = stock_yjbb_em_df[stock_yjbb_em_df["股票代码"].isin(list(self.code_mapping.keys()))]
            stock_yjbb_em_df = stock_yjbb_em_df.round(2)
            stock_yjbb_em_df = stock_yjbb_em_df.fillna("未知")
            for _, row in stock_yjbb_em_df.iterrows():
                code = row["股票代码"]
                tags = [code, func_tag]
                stock = self.code_mapping[code]
                md5 = self._md5(f"{code}_{row['最新公告日期'].strftime('%Y%m%d')}_performance_report")
                title = f"[{stock['code_name']}]{row['股票简称']} 业绩快报"

                text_doc = TextDoc(
                    md5=md5,
                    data_time=row["最新公告日期"].strftime("%Y-%m-%d") + " 00:00:00",
                    repo_id=stock["repo_id"],
                    title=title,
                    author="东方财富-业绩快报",
                    content=self._kv_text(row=row, exclude=["股票代码", "股票简称", "序号", "最新公告日期"]),
                    tags=tags,
                )
                await self.parsing(text_doc=text_doc)

    async def get_stock_performance_forecast(self):
        """
        业绩预告
        https://akshare.akfamily.xyz/data/stock/stock.html#id134
        """
        func_tag = "业绩预告"
        for q in ("20241231", "20250331", "20250630"):
            stock_yjyg_em_df = ak.stock_yjyg_em(date=q)
            stock_yjyg_em_df = stock_yjyg_em_df[stock_yjyg_em_df["股票代码"].isin([stock["code"] for stock in self.data_range])]
            stock_yjyg_em_df = stock_yjyg_em_df.round(2)

            for _, row in stock_yjyg_em_df.iterrows():
                code = row["股票代码"]
                stock = self.code_mapping[code]
                tags = [code, func_tag]
                md5 = self._md5(f"{code}_{row['公告日期'].strftime('%Y%m%d')}_{row['序号']}_performance_report")
                title = f"[{stock['code_name']}]{row['预测指标']}{row['预告类型']}"

                text_doc = TextDoc(
                    md5=md5,
                    data_time=row["公告日期"].strftime("%Y-%m-%d") + " 00:00:00",
                    repo_id=stock["repo_id"],
                    title=title,
                    author="东方财富-业绩预告",
                    content=self._kv_text(row=row, include=["业绩变动", "业绩变动原因"]),
                    tags=tags,
                )
                await self.parsing(text_doc=text_doc)


    async def get_stock_analyst_rank(self):
        """
        分析师指数+分析师详情
        https://akshare.akfamily.xyz/data/stock/stock.html#id93
        https://akshare.akfamily.xyz/data/stock/stock.html#id94
        """
        func_tag = "分析师意见"
        year = "2025"
        stock_analyst_rank_em_df = ak.stock_analyst_rank_em(year=year)
        base_stock_em_analyst_detail = None
        for _, row in stock_analyst_rank_em_df.iterrows():
            try:
                stock_em_analyst_detail_df = ak.stock_analyst_detail_em(analyst_id=row["分析师ID"], indicator="最新跟踪成分股")
            except Exception as err:
                continue
            stock_em_analyst_detail_df = stock_em_analyst_detail_df[stock_em_analyst_detail_df["股票代码"].isin(self.data_range.code)]
            if len(stock_em_analyst_detail_df) == 0:
                continue
            stock_em_analyst_detail_df["分析师"] = row["分析师名称"]
            stock_em_analyst_detail_df[f"分析师排名({year})"] = row["序号"]
            if base_stock_em_analyst_detail is None:
                base_stock_em_analyst_detail = stock_em_analyst_detail_df
            else:
                base_stock_em_analyst_detail = pd.concat([base_stock_em_analyst_detail, stock_em_analyst_detail_df], axis=0, ignore_index=True)

        for _, row in base_stock_em_analyst_detail.iterrows():
            code = row["股票代码"]
            stock = self.code_mapping[code]
            tags = [code, func_tag]
            md5 = self._md5(f"{code}_{row['最新评级日期'].strftime('%Y%m%d')}_{row['序号']}_analyst_rank")
            title = f"[{stock['code_name']}] {row['分析师']} 最新评级为[{row['当前评级名称']}]"

            text_doc = TextDoc(
                md5=md5,
                data_time=row["最新评级日期"].strftime("%Y-%m-%d") + " 00:00:00",
                repo_id=stock["repo_id"],
                title=title,
                author="东方财富-分析师评级",
                content=f"分析师：{row['分析师']}({year}年排名第{row['序号']})\n"+self._kv_text(row=row, include=["股票代码", "最新评级日期"]),
                tags=tags,
            )
            await self.parsing(text_doc=text_doc)


    async def get_stock_score(self):
        """
        千股千评
        https://akshare.akfamily.xyz/data/stock/stock.html#id95
        """
        func_tag = "千股千评"
        stock_comment_em_df = ak.stock_comment_em()
        stock_comment_em_df = stock_comment_em_df[stock_comment_em_df["代码"].isin([stock["code"] for stock in self.data_range])]
        stock_comment_em_df = stock_comment_em_df.round(2)
        for _, row in stock_comment_em_df.iterrows():
            code = row["代码"]
            stock = self.code_mapping[code]
            tags = [code, func_tag]
            md5 = self._md5(f"{code}_{row['交易日'].strftime('%Y%m%d')}_score")
            title = f"[{code}]{row['名称']} 千股千评评分"

            text_doc = TextDoc(
                md5=md5,
                data_time=row["交易日"].strftime("%Y-%m-%d") + " 15:00:00",
                repo_id=stock["repo_id"],
                title=title,
                author="东方财富-千股千评",
                content=self._kv_text(row=row, exclude=["序号", "代码", "名称", "交易日"]),
                tags=tags,
            )
            await self.parsing(text_doc=text_doc)


    async def get_stock_research_report(self):
        """
        个股研报
        https://akshare.akfamily.xyz/data/stock/stock.html#id175
        """
        func_tag = "个股研报"
        for stock in self.data_range:
            code = stock["code"]
            code_name = stock["code_name"]
            tags = [code, func_tag]
            last_data_time = await self.get_last_data_time(repo_id=stock["repo_id"], tags=tags)
            stock_research_report_em_df = ak.stock_research_report_em(symbol=code)
            stock_research_report_em_df["日期"] = stock_research_report_em_df["日期"].apply(lambda x: strftime(x, "%Y-%m-%d") + " 00:00:00")
            stock_research_report_em_df = stock_research_report_em_df[stock_research_report_em_df["日期"] > last_data_time]
            stock_research_report_em_df = stock_research_report_em_df.astype(str)
            for col in ("2025-盈利预测-收益", "2025-盈利预测-市盈率", "2026-盈利预测-收益", "2026-盈利预测-市盈率"):
                stock_research_report_em_df[col] = stock_research_report_em_df[col].replace("nan", "未预测")

            # logger.info(f"{doc_code} 新数据: {len(stock_research_report_em_df)}")
            for _, row in stock_research_report_em_df.iterrows():
                md5 = self._md5(row["报告PDF链接"])
                title = f"[{code_name}]{row['机构']}研报《{row['报告名称']}》"

                text_doc = TextDoc(
                    md5=md5,
                    data_time=row["日期"],
                    repo_id=stock["id"],
                    author=row["机构"],
                    url=row["报告PDF链接"],
                    title=title,
                    content=self._kv_text(row=row, include=["东财评级", "报告PDF链接", "行业", "股票代码", "机构", "2025-盈利预测-收益", "2025-盈利预测-市盈率", "2026-盈利预测-收益", "2026-盈利预测-市盈率"]),
                    tags=tags
                )
                await self.parsing(text_doc=text_doc)

    @staticmethod
    async def get_last_data_time(repo_id: int = None, tags: list[str] = None,
                                 non_start_time: str = "2025-01-01 00:00:00"):
        filters = []
        if tags:
            filters.extend([{"term": {"tags": tag} for tag in tags}])
        if repo_id:
            filters.append({"term": {"repo_id": repo_id}})

        res = await es.search(
            index=ALL_REPO_INDEX,
            query={
                "bool": {
                    "filter": filters
                }
            },
            sort={
                "data_time": {
                    "order": "desc"
                }
            },
            size=1,
            source_includes="data_time"
        )
        hits = res["hits"]["hits"]

        if not hits:
            return non_start_time

        return hits[0]["_source"]["data_time"]

    async def parsing(self, text_doc: TextDoc):
        text_doc.tenant_id = 1
        try:
            doc_id = await Doc.create(
                repo_id=text_doc.repo_id, name=text_doc.title, tags=text_doc.tags, size=0, md5=text_doc.md5,
                status=DocStatus.parsing, create_time=text_doc.data_time, update_time=text_doc.data_time,
                data_time=text_doc.data_time, reference_type=text_doc.reference_type)
        except AlreadyExistsError:
            logger.info(f"文档已存在: {text_doc.title}")
            return
        else:
            text_doc.doc_id = doc_id
        parser = TextDocParser(
            doc_id=text_doc.doc_id,
            text_doc=text_doc,
            chunker=HtmlChunker(max_tokens=1024, tokenizer=BGE_M3_TOKENIZER),
            embedding_engine=EmbeddingEngine(model=EmbeddingModel.BGE_M3),
            embedding_batch_size=10)
        await parser.exec()
        if DOC_EXTRACT_ENABLED:
            await self.doc_extract.kiq(doc_id=text_doc.doc_id)

    @staticmethod
    def _md5(v: str):
        return hashlib.md5(v.encode("utf-8")).hexdigest()

    @staticmethod
    def _kv_text(row: dict, include: list[str] = None, exclude: list[str] = None, ignore_nan_value: bool = False):
        if exclude is None:
            exclude = []
        text = ""
        for k, v in row.items():
            if include:
                if k not in include:
                    continue
            if k in exclude:
                continue
            if ignore_nan_value is True and (v is None or v.isnull()):
                continue
            text += f"{k}: {v}\n"
        return text

    @staticmethod
    def _kv_labels(row: dict, include: list[str] = None, exclude: list[str] = None):
        if exclude is None:
            exclude = []
        if include:
            return {k: v for k, v in row.items() if k in include and v}
        else:
            return {k: v for k, v in row.items() if k not in exclude and v}

    # tasks.doc_extract的占位方法
    @staticmethod
    @broker.task(task_name="doc_extract")
    async def doc_extract(doc_id: int):...


AkshareDataOffline = AkshareDataOfflineTask()
