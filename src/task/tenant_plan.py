#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from engine.rdb import g
from common.logger import logger
from controller.system import Tenant


class TenantPlanOfflineTask:
    @staticmethod
    async def refresh_period_tenant_plan():
        logger.info("开始刷新周期性套餐")
        wait_refresh_tenant_plan = await Tenant.get_refresh_tenant_plan()
        for tenant_plan in wait_refresh_tenant_plan:
            new_tenant_plan = await Tenant.refresh_plan(tenant_plan=tenant_plan)
            logger.info(f"为租户{tenant_plan['tenant_name']}刷新套餐{tenant_plan['plan_name']}成功\n"
                        f"刷新tokens:{new_tenant_plan.token_limit},刷新后剩余次数{new_tenant_plan.remain_refresh_count}\n"
                        f"下次刷新时间:{new_tenant_plan.refresh_time}")
            await g.session.commit()


TenantPlanOffline = TenantPlanOfflineTask()
