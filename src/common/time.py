import datetime

import dateutil.parser

from config import TZ


def now_datetime():
    """获取实时datetime"""
    return now_tz_datetime().replace(tzinfo=None)


def now_datetime_str(fmt: str = "%Y-%m-%d %H:%M:%S"):
    return now_datetime().strftime(format=fmt)


def now_tz_datetime():
    """获取带TIMEZONE的datetime"""
    return datetime.datetime.now(tz=TZ)


def init_datetime():
    """初始化数据时间"""
    return strptime("1970-01-01 00:00:00")


def strftime(date: datetime.datetime, fmt: str = "%Y-%m-%d %H:%M:%S"):
    return date.strftime(fmt)


def now_tz_datestring(fmt: str = "%Y-%m-%d %H:%M:%S"):
    return strftime(now_datetime(), fmt=fmt)


def now_tz_datestring_with_millis(fmt: str = "%Y-%m-%d %H:%M:%S.%f"):
    return strftime(now_datetime(), fmt=fmt)


def strptime(datestr: str, fmt=None):
    if fmt is not None:
        return datetime.datetime.strptime(datestr, fmt)
    else:
        return dateutil.parser.parse(datestr)



def days_date_range(start_date: datetime.datetime, end_date: datetime.datetime):
    """获取时间周期列表"""
    dates = []
    dt = datetime.datetime.strptime(start_date, "%Y-%m-%d")
    date = start_date[:]
    while date <= end_date:
        dates.append(date)
        dt = dt + datetime.timedelta(days=1)
        date = dt.strftime("%Y-%m-%d")
    return dates


def time_difference(date1: (str, datetime.datetime), date2: (str, datetime.datetime)):
    """获取时间差"""
    if isinstance(date1, str):
        date1 = strptime(date1)
    if isinstance(date2, str):
        date2 = strptime(date2)
    return date2 - date1


def convert_to_search_interval(start: (datetime.datetime, str), end: (datetime.datetime, str), length: int = 19):
    """
    转换%Y-%m-%d时间格式为%Y-%m-%d %H:%M:%S

    example:
        start: 2020-03-01将被转换为2020-03-01 00:00:00
        end: 2020-03-31将被转换为2020-03-31 23:59:59

    :param start: 起始时间
    :param end: 结束时间
    :param length: 时间长度
    :return: 起始和结束时间
    """
    start = convert_to_search_start_time(start)[:length]
    end = convert_to_search_end_time(end)[:length]
    return start, end


def convert_to_search_start_time(start: (datetime.datetime, str)):
    if isinstance(start, str) and len(start) == 19:
        return start
    if isinstance(start, datetime.datetime):
        start = start.date()
    else:
        start = datetime.datetime.strptime(start, "%Y-%m-%d")
    start = start.strftime("%Y-%m-%d %H:%M:%S")
    return start


def convert_to_search_end_time(end: (datetime.datetime, str)):
    if isinstance(end, str) and len(end) == 19:
        return end

    if not isinstance(end, datetime.datetime):
        end = datetime.datetime.strptime(end, "%Y-%m-%d")
        end = end.replace(hour=23, minute=59, second=59)

    end = end.strftime("%Y-%m-%d %H:%M:%S")
    return end


def is_valid_datetime(value: str):
    try:
        dateutil.parser.parse(value)
        return True
    except Exception:
        return False


def custom_serializer(obj: dict):
    # 如果对象是 datetime 类型，转换为字符串
    if isinstance(obj, datetime.datetime):
        return strftime(obj)
    raise TypeError(f"Type {type(obj)} not serializable")


def time_delta_translate(period: str):
    try:
        num = int(period[:-1])
        unit = period[-1].lower()
    except (ValueError, IndexError):
        raise ValueError(f"Invalid time format: {period}. Expected format like '1d', '2w', etc.")

    if unit == "h":
        return datetime.timedelta(hours=num)
    elif unit == "d":
        return datetime.timedelta(days=num)
    elif unit == "w":
        return datetime.timedelta(weeks=num)
    elif unit == "m":
        return datetime.timedelta(days=num * 30)
    elif unit == "y":
        return datetime.timedelta(days=num * 365)
    else:
        raise ValueError("不支持的时间单位")


def ceil_to_next_day(dt: datetime.datetime):
    # 如果已经是00:00:00，则不需要向上取整
    if dt.hour == 0 and dt.minute == 0 and dt.second == 0 and dt.microsecond == 0:
        return dt
    # 直接加一天并替换时间为00:00:00
    return (dt + datetime.timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0)