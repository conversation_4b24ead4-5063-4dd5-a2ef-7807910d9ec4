from datetime import datetime, timed<PERSON><PERSON>


def ceil_to_next_day(date_str):
    # 解析时间字符串
    dt = datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')

    # 向上取整到下一天的00:00:00
    next_day = dt.date() + timedelta(days=1)
    result = datetime.combine(next_day, datetime.min.time())

    return result


# 测试
original_time = "2025-08-17 18:18:18"
rounded_time = ceil_to_next_day(original_time)
print(rounded_time)