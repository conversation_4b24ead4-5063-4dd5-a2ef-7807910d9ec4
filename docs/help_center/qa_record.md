# 问答记录

---

查看租户下所有用户的问答记录和召回结果，检查RAG召回效果上的相关问题。

在列表页可见以下字段内容：

* 问答类型：分为
  * `单文档问答`：在文档详情页面进行问答
  * `数据问答`：在数据问答页面进行问答
* 关联知识库：问答所选择的知识库范围
* 会话名称：由AI生成或用户修改的会话名称
* 用户输入：用户输入的问题
* 改写后问题：根据上下文和AI意图分析改写的问题，将实际应用于召回
* 思考过程：如果是thinking模型，将显示AI的思考过程
* 回答内容：AI的回答内容
* 索引耗时：工程端查询数据范围、构造策略、数据对象等产生的时间
* 检索耗时：进行数据检索(retrieval)的过程耗时，如果同时选用了知识库和网络搜索，取其更长的耗时。需要注意其中页包含了使用大模型进行问题改写扩写，可能是检索过程较长的原因之一
* 排序耗时：进行各类数据排序(rerank)的过程耗时
* 召回耗时：索引过程+检索过程+排序过程的总时间，体现了整体召回性能
* 首字延迟：片段组织和发送给大模型后，大模型首个token的反应时间，体现大模型的性能和负载
* 总输出耗时：从大模型第一个token响应开始算起，直至输出结束，总计使用的时间
* 模型名称：使用的大模型
* 发起人：使用此功能的用户昵称
* 问答状态：问答的状态信息。如果状态非`问答成功`，那么前置的部分参数可能无值

详情页额外的信息包括：
* 参考文档：如果选用了知识库问答，那么在召回时检索出的文档范围
* 溯源片段：大模型参考所有召回后，最终所选择引用溯源的片段
* 知识库召回片段：知识库召回后返回的片段内容
* 网络搜索召回片段：网络搜索召回后返回的片段内容