# 数据问答

---

数据问答可同时对知识库和网络上的数据进行RAG召回和问答，支持多种大模型选择，对应搜索引擎的高级配置，以及用户侧检索关键词权重等高级配置。
为了优化、排查、DEBUG、记录等原因，**问答功能会记录问答日志**，仅租户管理员可见。

## 问答

### 大模型选择
可选择已注册的问答模型。相对来说，非推理模型建议使用DeepSeek-V3.1，推理模型建议使用DeepSeek-V3.1-Think。

### 知识库选择
可选择任意知识库进行问答，包括租户内的公共知识库，和由自己创建的私有知识库，或不选择知识库进行问答。

### 搜索引擎选择
可选择`Tavily`或`智谱Pro`进行网络搜索补充知识库数据

#### 通用能力
**深度抽取**：是否需要调用爬虫深入网页，先对html代码进行分析和切片，再RAG召回。该功能**会过滤不可访问的地址**，并且可能带来更好的召回/溯源体验，但也会显著增加资源使用和耗时。
> 由于该功能会直接进入对应网页，在私有化部署的网络无法达到目标页面时，请勿开启该功能，避免无法找到网页内容。

#### 能力对比

> 网页忠实度：指返回的路由在常规公网环境下路由和内容的匹配程度。有时会因常规公网的可达程度、数据过时、路由错误等原因，导致搜索引擎提供的路由和内容无法匹配。

|        | 境内数据支持 | 境外数据支持 | 网页忠实度 | 最大数据量 | 选定网址 | 黑名单网址 | 
|--------|--------|--------|-------|-------|------|-------|
| Tavily | 较差     | 较好     | 较高    | 20    | 支持单个 | 支持    |
| 智谱pro  | 较好     | 较差     | 较低    | 50    | 支持批量 | 不支持   |


### 用户检索

用户可以配置多个关键词，通过不同的字段位置，进行召回权重的控制，以实现用户自定义检索的能力。当该位置出现该词时，规则即刻生效，**多个关键词命中时效果叠乘**。
> 一般建议使用少量词配置，并且权重控制在[0.5, 2] 过大或过小的单个词有可能会影响整体的RAG。

## 溯源
大模型作答后，可见引用文档的角标。角标可hover渲染html片段，也可通过全文溯源进入文中对应位置并高亮。溯源的pdf/html方式等同于解析方式。