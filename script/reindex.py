#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import asyncio
from typing import List, Dict, Any

from elasticsearch import AsyncElasticsearch
from engine.es import es, ES_URL
from model.doc import get_index, DOC_MAPPING, WEB_DOC_INDEX
from model.analytics_metrics_es import METRICS_INDEX, METRICS_MAPPING
from model.summarizer_task_es import SUMMARIZER_TASK_INDEX, SUMMARIZER_TASK_MAPPING
from model.deep_research_es import DEEP_RESEARCH_INDEX, DEEP_RESEARCH_MAPPING


NEW_ES_URL = "******************************@192.168.0.27:29200"


async def get_all_indices(es_client: AsyncElasticsearch) -> List[str]:
    """获取所有索引名称"""
    try:
        indices_response = await es_client.cat.indices(format="json")
        return [index["index"] for index in indices_response if not index["index"].startswith(".")]
    except Exception as e:
        print(f"获取索引列表失败: {e}")
        return []


async def create_index_with_mapping(es_client: AsyncElasticsearch, index_name: str, mapping: Dict[str, Any]) -> bool:
    """在新ES节点创建索引和mapping"""
    try:
        # 检查索引是否已存在
        if await es_client.indices.exists(index=index_name):
            print(f"索引 {index_name} 已存在，跳过创建")
            return True

        # 创建索引
        response = await es_client.indices.create(index=index_name, body=mapping)
        if response.get("acknowledged"):
            print(f"索引 {index_name} 创建成功")
            return True
        else:
            print(f"索引 {index_name} 创建失败: {response}")
            return False
    except Exception as e:
        print(f"创建索引 {index_name} 失败: {e}")
        return False


async def reindex_data(old_es: AsyncElasticsearch, new_es: AsyncElasticsearch,
                      source_index: str, target_index: str) -> bool:
    """执行数据迁移"""
    try:
        # 使用reindex API进行数据迁移
        reindex_body = {
            "source": {
                "remote": {
                    "host": ES_URL
                },
                "index": source_index
            },
            "dest": {
                "index": target_index
            }
        }

        print(f"开始迁移数据: {source_index} -> {target_index}")
        response = await new_es.reindex(body=reindex_body, wait_for_completion=True, timeout="30m")

        if response.get("timed_out"):
            print(f"数据迁移超时: {source_index} -> {target_index}")
            return False

        total = response.get("total", 0)
        created = response.get("created", 0)
        print(f"数据迁移完成: {source_index} -> {target_index}, 总计: {total}, 创建: {created}")
        return True

    except Exception as e:
        print(f"数据迁移失败 {source_index} -> {target_index}: {e}")
        return False


def get_mapping_for_index(index_name: str) -> Dict[str, Any]:
    """根据索引名称获取对应的mapping"""
    # 如果索引以repo开头或等于WEB_DOC_INDEX，使用DOC_MAPPING
    if index_name.startswith("repo") or index_name == WEB_DOC_INDEX:
        return DOC_MAPPING

    # 如果索引等于METRICS_INDEX，使用METRICS_MAPPING
    elif index_name == METRICS_INDEX:
        return METRICS_MAPPING

    # 如果索引等于SUMMARIZER_TASK_INDEX，使用SUMMARIZER_TASK_MAPPING
    elif index_name == SUMMARIZER_TASK_INDEX:
        return SUMMARIZER_TASK_MAPPING

    # 如果索引等于DEEP_RESEARCH_INDEX，使用DEEP_RESEARCH_MAPPING
    elif index_name == DEEP_RESEARCH_INDEX:
        return DEEP_RESEARCH_MAPPING

    # 其他索引不处理
    else:
        return None


async def reindex():
    """主要的reindex函数"""
    # 创建新ES客户端连接
    new_es = AsyncElasticsearch(NEW_ES_URL, request_timeout=60, max_retries=3, retry_on_timeout=True)

    try:
        # 获取旧节点的所有索引
        print("获取旧节点索引列表...")
        old_indices = await get_all_indices(es)
        print(f"找到 {len(old_indices)} 个索引: {old_indices}")

        # 过滤需要迁移的索引
        indices_to_migrate = []
        for index_name in old_indices:
            mapping = get_mapping_for_index(index_name)
            if mapping is not None:
                indices_to_migrate.append((index_name, mapping))

        print(f"需要迁移的索引: {[idx[0] for idx in indices_to_migrate]}")

        # 为每个需要迁移的索引创建mapping并迁移数据
        for index_name, mapping in indices_to_migrate:
            print(f"\n处理索引: {index_name}")

            # 在新节点创建索引和mapping
            if await create_index_with_mapping(new_es, index_name, mapping):
                # 执行数据迁移
                await reindex_data(es, new_es, index_name, index_name)
            else:
                print(f"跳过索引 {index_name} 的数据迁移")

        print("\n所有索引迁移完成!")

    except Exception as e:
        print(f"reindex过程中发生错误: {e}")
    finally:
        # 关闭新ES连接
        await new_es.close()


if __name__ == "__main__":
    asyncio.run(reindex())