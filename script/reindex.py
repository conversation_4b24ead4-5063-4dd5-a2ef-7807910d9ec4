#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from engine.es import es, ES_URL
from model.doc import get_index, DOC_MAPPING, WEB_DOC_INDEX
from model.analytics_metrics_es import METRICS_INDEX, METRICS_MAPPING
from model.summarizer_task_es import SUMMARIZER_TASK_INDEX, SUMMARIZER_TASK_MAPPING
from model.deep_research_es import DEEP_RESEARCH_INDEX, DEEP_RESEARCH_MAPPING


NEW_ES_URL = "******************************@124.71.176.31:29200"

async def reindex():
    pass