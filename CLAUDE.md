# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

LLM_DA（LLM Data Analytics）是一个基于大型语言模型的智能数据分析平台，主要功能包括：
- 自由问答（聊天）功能
- 文档管理与摘要功能  
- 知识库交互与文档问答
- 数据分析与统计功能
- 深入研究功能
- 用户权限管理系统

项目使用Python技术栈，基于FastAPI框架，支持异步处理和流式响应。

## 开发环境和运行命令

### 启动开发服务器
```bash
# 直接运行
python src/server.py

# 使用uvicorn
uvicorn src.server:app --host 0.0.0.0 --port 10001 --reload

# 使用gunicorn生产环境
gunicorn -c src/gunicorn.conf.py src.server:app
```

### 任务管理
```bash
# 运行后台任务
python src/tasks.py

# 启动任务调度器（如果需要）
taskiq scheduler src.tasks:scheduler
```

### Docker部署
```bash
# 构建镜像
docker build -t llm-da .

# 运行容器
docker run -p 10001:10001 llm-da
```

### 依赖管理
```bash
# 安装依赖
pip install -r requirements.txt

# 使用中国镜像源
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

## 项目架构

### 目录结构
项目采用清晰的分层MVC架构：

```
src/
├── model/           # 数据模型层（SQLAlchemy ORM）
├── view/            # 视图层（FastAPI路由和接口定义）
├── controller/      # 控制器层（业务逻辑）
├── common/          # 通用工具和辅助函数
├── engine/          # 核心引擎组件（数据库、缓存、文件系统）
├── config/          # 配置文件
├── task/            # 异步任务定义
├── exception/       # 异常处理
└── deploy/          # 部署相关
```

### 核心组件

#### 1. 控制器层结构 (`src/controller/`)
- `chat/` - 聊天和对话功能
- `analytics/` - 数据分析和仪表盘
- `admin/` - 管理员功能（用户、角色、租户）
- `engine/` - LLM引擎和嵌入模型
- `operator/` - 业务操作（文档处理、检索、分析）
- `repository/` - 知识库管理
- `parser/` - 文档解析器（支持多种格式）
- `retriever/` - 检索器（混合检索、语义检索）

#### 2. 模型层特点 (`src/model/`)
- 使用SQLAlchemy 2.0的新式声明语法
- 所有模型继承自`BaseModel`基类
- 支持MySQL数据库和Elasticsearch搜索引擎
- 类型提示完整，使用`Mapped`类型注解

#### 3. 视图层路由 (`src/view/`)
- RESTful API设计
- 使用FastAPI依赖注入系统
- 支持OpenAPI文档自动生成
- 路由按功能模块组织

## 技术栈和外部依赖

### 主要技术栈
- **Web框架**: FastAPI + Uvicorn/Gunicorn
- **数据库**: MySQL (通过SQLAlchemy ORM)
- **搜索引擎**: Elasticsearch
- **缓存**: Redis
- **任务队列**: Taskiq + Redis
- **文档解析**: 支持多种解析器（Docling、MinerU等）
- **LLM集成**: 支持多种模型（OpenAI、通义千问、Anthropic等）

### 外部服务集成
- **对象存储**: MinIO
- **搜索API**: Tavily、智谱GLM搜索
- **文档解析**: LibreOffice、自定义解析服务
- **LLM服务**: 支持多种API端点和模型

## 环境配置

项目使用`.env`文件进行环境配置。主要配置项包括：

### 数据库配置
```
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=password
MYSQL_DB=llm_da
```

### Redis配置
```
REDIS_HOST=localhost  
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_CACHE_DB=0
TASKIQ_BROKER_DB=1
```

### LLM配置
```
LLM_API_URL=https://api.bchampion.cn/v1
LLM_API_KEY=your_api_key
```

### 其他服务
```
ES_URL=http://localhost:9200
MINIO_URL=localhost:9000
TAVILY_API_KEY=your_tavily_key
```

## 开发规范与风格指南

### 1. 代码组织与架构规范

#### MVC分层架构
- **模型层（Model）**: 数据模型定义在`src/model/`，使用SQLAlchemy 2.0+语法
- **视图层（View）**: API路由定义在`src/view/`，使用FastAPI装饰器和依赖注入
- **控制器层（Controller）**: 业务逻辑在`src/controller/`，按功能模块组织
- **通用组件**: 工具函数在`src/common/`，引擎组件在`src/engine/`

#### 模块化组织原则
- 按功能垂直切分：`chat/`、`analytics/`、`repository/`等
- 每个功能模块包含完整的MVC组件
- 通用功能抽取到`common/`和`engine/`层

### 2. 命名规范

#### Python命名约定
- **类名**: 使用PascalCase（如`UserModel`、`ChatHelper`、`ApiError`）
- **函数和变量**: 使用snake_case（如`session_id`、`get_query`、`fetch_all`）
- **常量**: 使用UPPER_CASE（如`MYSQL_HOST`、`LOGGING_LEVEL`、`MAX_HISTORY_LEN`）
- **模块文件**: 使用小写+下划线（如`custom_exception.py`、`base_view.py`）

#### 业务命名规范
- **控制器类**: 以`Controller`结尾（如`UserController`）
- **模型类**: 以`Model`结尾（如`UserModel`、`SessionModel`）
- **视图类**: 以`View`结尾（如`ChatView`、`BaseView`）
- **异常类**: 以`Error`结尾（如`ApiError`、`NotFoundError`）

### 3. 代码风格与编程习惯

#### 基础代码风格
- **PEP 8规范**: 严格遵循Python官方代码风格指南
- **中文注释**: 类和函数使用中文文档字符串，行内注释使用中文
- **类型提示**: 广泛使用Type Hints，包括函数参数、返回值和变量
- **字符串格式**: 优先使用f-string进行字符串格式化

#### 导入组织规范
```python
# 1. 标准库导入
import os
import logging
from typing import List, Optional

# 2. 第三方库导入
from fastapi import FastAPI, Depends
from sqlalchemy import select
from pydantic import BaseModel

# 3. 本地模块导入
from common import g
from model.user import UserModel
from exception import ApiError
```
- **导入顺序**: 按照标准库、第三方库、本地模块的顺序组织导入，实际代码中无需添加导入注释

#### 装饰器使用模式
- **API装饰器**: 使用`@api_description`统一API文档
- **数据库会话**: 使用`@load_session_context`处理数据库会话
- **时间统计**: 使用`@async_time_cost`记录函数执行时间
- **路由注册**: 通过BaseView自动注册路由

### 4. 异步编程模式

#### 异步函数规范
- **async/await语法**: 所有I/O密集型操作使用异步
- **异步生成器**: 流式响应使用`async def generator()`模式
- **上下文管理**: 使用`async with`进行资源管理

#### 并发处理模式
```python
# 异步生成器模式
async def generator(self):
    async for message in response:
        yield message

# 上下文管理器模式
async with sessionmanager.session() as session:
    # 数据库操作
    pass
```

### 5. 数据库操作规范

#### SQLAlchemy 2.0+新式语法
- **模型定义**: 使用`Mapped`类型注解和`mapped_column`
- **查询构造**: 使用`select()`函数构造查询
- **会话管理**: 通过依赖注入或装饰器管理会话生命周期

#### 数据库会话模式
```python
# 装饰器模式
@load_session_context
async def some_function():
    # 使用g.session进行数据库操作
    pass

# 依赖注入模式
async def api_handler(session: session_type):
    # 直接使用session参数
    pass
```

#### 查询工具函数
- **分页查询**: 使用`paginator()`统一处理分页
- **数据获取**: 使用`fetch_all()`和`fetch_one()`简化查询
- **排序处理**: 使用`query_order()`统一排序逻辑

### 6. 错误处理与日志记录

#### 异常体系设计
- **基础异常**: 所有业务异常继承自`ApiError`
- **错误码管理**: 在`error_code.py`中集中定义错误码和消息
- **HTTP状态映射**: 错误码自动映射到HTTP状态码

#### 异常处理模式
```python
# 自定义异常抛出
raise NotFoundError(message="用户不存在", data={"user_id": 123})

# 异常类定义
class NotFoundError(ApiError):
    default_code = DataNotFoundCode
```

#### 日志记录规范
- **loguru配置**: 统一使用loguru进行日志记录
- **日志分级**: DEBUG、INFO、ERROR日志分文件存储
- **日志轮转**: 按天轮转，自动清理过期日志
- **结构化日志**: 支持trace_id跟踪和性能监控

### 7. 依赖注入与配置管理

#### FastAPI依赖注入
- **数据库会话**: 通过`Depends(get_db)`注入
- **用户认证**: 通过认证类依赖注入
- **路由依赖**: 在BaseView中自动处理依赖关系

#### 配置管理模式
- **环境配置**: 使用`.env`文件和`os.getenv()`
- **配置分类**: 按服务类型分组配置（数据库、Redis、LLM等）
- **类型转换**: 配置值进行适当的类型转换
- **默认值**: 为所有配置提供合理的默认值

#### 上下文变量管理
```python
# 全局上下文变量
from common import g

# 设置上下文变量
g.user_id = user_id
g.session = session
g.trace_id = trace_id

# 在业务逻辑中使用
current_user_id = g.user_id
```

### 8. API设计规范

#### RESTful API设计
- **HTTP方法**: GET（查询）、POST（创建）、PUT（更新）、DELETE（删除）
- **URL命名**: 使用名词复数形式，避免动词
- **状态码**: 正确使用HTTP状态码表示操作结果

#### 响应格式统一
```python
# 标准响应格式
{
    "code": 0,
    "message": "请求成功",
    "trace_id": "unique_trace_id",
    "data": {
        # 响应数据
    }
}
```

#### 流式响应处理
- **媒体类型**: 使用`text/event-stream`
- **异步生成**: 使用异步生成器产生流数据
- **错误处理**: 流中包含错误状态和消息

### 9. 开发最佳实践

#### 代码质量保证
- **类型检查**: 使用完整的类型注解
- **代码复用**: 抽取通用逻辑到基类和工具函数
- **模块化设计**: 保持高内聚低耦合
- **接口抽象**: 定义清晰的接口边界

#### 性能优化原则
- **异步优先**: I/O密集型操作全部异步化
- **连接池管理**: 数据库和缓存使用连接池
- **资源清理**: 使用上下文管理器确保资源释放
- **缓存策略**: 合理使用Redis缓存提升性能

#### 安全编程规范
- **参数验证**: 使用Pydantic进行输入验证
- **SQL注入防护**: 使用ORM参数化查询
- **认证授权**: 完整的用户认证和权限控制
- **敏感信息**: 不在代码中硬编码敏感信息

## 部署说明

### Docker部署
项目包含完整的Dockerfile，支持容器化部署。镜像基于Python 3.11-slim，包含LibreOffice等文档处理工具。

### 生产环境
- 使用Gunicorn + Uvicorn Worker
- 支持多进程部署
- 配置文件：`src/gunicorn.conf.py`
- 默认端口：10001

### 健康检查
项目提供健康检查端点：`/om/healthcheck`