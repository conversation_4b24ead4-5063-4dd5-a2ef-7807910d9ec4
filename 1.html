<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <style>
        body {
            display: flex;
            justify-content: center;
            align-items: flex-start;
            min-height: 100vh;
            margin: 0;
            background-color: #f9f9f9;
            padding: 20px 0;
        }

        .markdown-body {
            box-sizing: border-box;
            max-width: 980px;
            width: 100%;
            padding: 45px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        /* 改进的列表样式 */
        ul {
            margin: 10px 0;
            padding-left: 20px;
        }

        ul ul {
            margin: 5px 0;
            padding-left: 25px;
        }

        li {
            margin: 8px 0;
            line-height: 1.6;
        }

        /* 表格样式 */
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        th,
        td {
            border: 1px solid #ddd;
            padding: 12px 8px;
            text-align: left;
        }

        th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        tr:hover {
            background-color: #e9ecef;
        }

        /* 引用样式 */
        blockquote {
            border-left: 4px solid #007bff;
            margin: 20px 0;
            padding: 15px 20px;
            background-color: #f8f9fa;
            border-radius: 0 4px 4px 0;
            color: #495057;
        }

        blockquote p {
            margin: 0 0 10px 0;
        }

        blockquote p:last-child {
            margin-bottom: 0;
        }

        blockquote blockquote {
            border-left: 4px solid #6c757d;
            background-color: #e9ecef;
            margin: 10px 0;
        }

        /* 代码样式 */
        pre {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            overflow-x: auto;
            margin: 15px 0;
        }

        code {
            background-color: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.9em;
        }

        pre code {
            background-color: transparent;
            padding: 0;
            border-radius: 0;
        }

        /* 水平分割线 */
        hr {
            border: none;
            border-top: 2px solid #e9ecef;
            margin: 30px 0;
        }

        /* 任务列表样式 */
        .task-list-item {
            list-style: none;
        }

        .task-list-item input[type="checkbox"] {
            margin-right: 8px;
        }

        /* 删除线、标记等样式 */
        del {
            color: #6c757d;
        }

        mark {
            background-color: #fff3cd;
            padding: 2px 4px;
            border-radius: 2px;
        }

        ins {
            background-color: #d4edda;
            text-decoration: none;
            padding: 2px 4px;
            border-radius: 2px;
        }

        sup,
        sub {
            font-size: 0.8em;
        }

        /* 标题锚点样式 */
        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            position: relative;
        }

        @media (max-width: 767px) {
            .markdown-body {
                padding: 15px;
            }
        }
    </style>
</head>

<body>
    <div class="markdown-body">
        <h1 id="数据问答">数据问答</h1>
        <hr />
        <p>数据问答可同时对知识库和网络上的数据进行RAG召回和问答，支持多种大模型选择，对应搜索引擎的高级配置，以及用户侧检索关键词权重等高级配置。
            为了优化、排查、DEBUG、记录等原因，<strong>问答功能会记录问答日志</strong>，仅租户管理员可见。</p>
        <h2 id="问答">问答</h2>
        <h3 id="大模型选择">大模型选择</h3>
        <p>可选择已注册的问答模型。相对来说，非推理模型建议使用DeepSeek-V3.1，推理模型建议使用DeepSeek-V3.1-Think。</p>
        <h3 id="知识库选择">知识库选择</h3>
        <p>可选择任意知识库进行问答，包括租户内的公共知识库，和由自己创建的私有知识库，或不选择知识库进行问答。</p>
        <h3 id="搜索引擎选择">搜索引擎选择</h3>
        <p>可选择<code>Tavily</code>或<code>智谱Pro</code>进行网络搜索补充知识库数据</p>
        <h4 id="通用能力">通用能力</h4>
        <p><strong>深度抽取</strong>：是否需要调用爬虫深入网页，先对html代码进行分析和切片，再RAG召回。该功能<strong>会过滤不可访问的地址</strong>，并且可能带来更好的召回/溯源体验，但也会显著增加资源使用和耗时。
        </p>
        <blockquote>
            <p>由于该功能会直接进入对应网页，在私有化部署的网络无法达到目标页面时，请勿开启该功能，避免无法找到网页内容。</p>
        </blockquote>
        <h4 id="能力对比">能力对比</h4>
        <blockquote>
            <p>网页忠实度：指返回的路由在常规公网环境下路由和内容的匹配程度。有时会因常规公网的可达程度、数据过时、路由错误等原因，导致搜索引擎提供的路由和内容无法匹配。</p>
        </blockquote>
        <table>
            <thead>
                <tr>
                    <th></th>
                    <th>境内数据支持</th>
                    <th>境外数据支持</th>
                    <th>网页忠实度</th>
                    <th>最大数据量</th>
                    <th>选定网址</th>
                    <th>黑名单网址</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Tavily</td>
                    <td>较差</td>
                    <td>较好</td>
                    <td>较高</td>
                    <td>20</td>
                    <td>支持单个</td>
                    <td>支持</td>
                </tr>
                <tr>
                    <td>智谱pro</td>
                    <td>较好</td>
                    <td>较差</td>
                    <td>较低</td>
                    <td>50</td>
                    <td>支持批量</td>
                    <td>不支持</td>
                </tr>
            </tbody>
        </table>
        <h3 id="用户检索">用户检索</h3>
        <p>用户可以配置多个关键词，通过不同的字段位置，进行召回权重的控制，以实现用户自定义检索的能力。当该位置出现该词时，规则即刻生效，<strong>多个关键词命中时效果叠乘</strong>。</p>
        <blockquote>
            <p>一般建议使用少量词配置，并且权重控制在[0.5, 2] 过大或过小的单个词有可能会影响整体的RAG。</p>
        </blockquote>
        <h2 id="溯源">溯源</h2>
        <p>大模型作答后，可见引用文档的角标。角标可hover渲染html片段，也可通过全文溯源进入文中对应位置并高亮。溯源的pdf/html方式等同于解析方式。</p>

    </div>
</body>

</html>